/**
 * CAM硬件相机相关样式模块
 * 包含CAM按钮、图片容器、状态指示器、加载和错误状态等样式
 */

/* CAM按钮激活状态 */
.ios-btn.cam-active {
  background: rgba(50, 205, 50, 0.2) !important;
  border: 2px solid rgba(50, 205, 50, 0.6);
  animation: camPulse 2s ease-in-out infinite;
}

@keyframes camPulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(50, 205, 50, 0.4); 
  }
  50% { 
    box-shadow: 0 0 0 6px rgba(50, 205, 50, 0); 
  }
}

/* CAM图片容器 */
.cam-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 12rpx;
  overflow: hidden;
}

/* CAM图片样式 */
.cam-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.1s ease-in-out;
  border-radius: 12rpx;
}

.cam-image.cam-active {
  opacity: 1;
  z-index: 2;
}

.cam-image.cam-hidden {
  opacity: 0;
  z-index: 1;
}

/* CAM状态指示器 */
.cam-status-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  z-index: 10;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.cam-status-indicator.connecting {
  background: rgba(255, 165, 0, 0.8);
  animation: connecting 1.5s ease-in-out infinite;
}

.cam-status-indicator.connected {
  background: rgba(50, 205, 50, 0.8);
}

.cam-status-indicator.error {
  background: rgba(220, 20, 60, 0.8);
  animation: errorBlink 1s ease-in-out infinite;
}

@keyframes connecting {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes errorBlink {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

/* CAM加载容器 */
.cam-loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12rpx;
}

.cam-loading-container.show {
  opacity: 1;
}

.cam-loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cam-loading-ring {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(50, 205, 50, 0.3);
  border-top: 4rpx solid rgba(50, 205, 50, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.cam-loading-dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(50, 205, 50, 0.8);
  border-radius: 50%;
  margin-top: -30rpx;
  animation: dot-move 1s linear infinite;
}

.cam-loading-text {
  color: rgba(50, 205, 50, 0.9);
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 30rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.cam-loading-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  margin-top: 16rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

@keyframes dot-move {
  0% { transform: rotate(0deg) translateX(30rpx) rotate(0deg); }
  100% { transform: rotate(360deg) translateX(30rpx) rotate(-360deg); }
}

/* CAM错误容器 */
.cam-error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 110;
  border-radius: 12rpx;
}

.cam-error-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(20, 20, 20, 0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 2px solid rgba(220, 20, 60, 0.3);
  max-width: 80%;
}

.cam-error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: errorShake 0.5s ease-in-out;
}

.cam-error-text {
  color: rgba(220, 20, 60, 0.9);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.cam-error-tip {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* CAM错误操作按钮区 */
.cam-error-actions {
  display: flex;
  gap: 30rpx;
  margin-top: 20rpx;
}

.cam-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  border: 2px solid transparent;
}

.cam-action-btn.retry-btn {
  background: rgba(50, 205, 50, 0.15);
  border-color: rgba(50, 205, 50, 0.3);
}

.cam-action-btn.retry-btn:active {
  background: rgba(50, 205, 50, 0.25);
  transform: scale(0.95);
}

.cam-action-btn.back-btn {
  background: rgba(70, 130, 180, 0.15);
  border-color: rgba(70, 130, 180, 0.3);
}

.cam-action-btn.back-btn:active {
  background: rgba(70, 130, 180, 0.25);
  transform: scale(0.95);
}

.cam-action-btn .action-btn-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.cam-action-btn .action-btn-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* CAM模式下的视觉处理优化 */
.cam-image-container .vision-view {
  /* 确保CAM图片在视觉处理模式下正确显示 */
  border-radius: 12rpx;
  overflow: hidden;
}

/* 硬件相机模式提示 */
.cam-mode-tip {
  position: absolute;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.cam-mode-info {
  background: rgba(255, 193, 7, 0.9);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
  border: 1px solid rgba(255, 193, 7, 0.5);
  backdrop-filter: blur(10px);
  min-width: 300rpx;
}

.light-theme-content .cam-mode-info {
  background: rgba(255, 152, 0, 0.9);
  box-shadow: 0 4px 16px rgba(255, 152, 0, 0.3);
  border-color: rgba(255, 152, 0, 0.5);
}

.cam-mode-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.cam-mode-text {
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 6rpx;
  text-align: center;
}

.cam-mode-desc {
  font-size: 22rpx;
  color: rgba(0, 0, 0, 0.7);
  text-align: center;
  line-height: 1.3;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .cam-mode-tip {
    top: 80rpx;
  }
  
  .cam-mode-info {
    padding: 16rpx 24rpx;
    min-width: 260rpx;
  }
  
  .cam-mode-icon {
    font-size: 28rpx;
  }
  
  .cam-mode-text {
    font-size: 24rpx;
  }
  
  .cam-mode-desc {
    font-size: 20rpx;
  }
  
  .cam-loading-box,
  .cam-error-box {
    padding: 40rpx 30rpx;
    max-width: 90%;
  }
  
  .cam-loading-text,
  .cam-error-text {
    font-size: 28rpx;
  }
  
  .cam-loading-desc,
  .cam-error-tip {
    font-size: 22rpx;
  }
  
  .cam-error-actions {
    gap: 20rpx;
  }
  
  .cam-action-btn {
    padding: 16rpx 24rpx;
    min-width: 100rpx;
  }
  
  .cam-action-btn .action-btn-icon {
    font-size: 36rpx;
  }
  
  .cam-action-btn .action-btn-text {
    font-size: 22rpx;
  }
}
