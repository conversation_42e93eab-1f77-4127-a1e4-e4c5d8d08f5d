/**
 * 视觉参数管理模块
 * 负责处理视觉参数的调整、重置和相关UI逻辑
 * 从ui-controller.js中提取视觉参数相关功能
 */

/**
 * 显示/隐藏视觉参数调整面板
 * @param {Object} page - 页面实例
 */
function toggleVisionParams(page) {
  page.setData({
    showVisionParams: !page.data.showVisionParams
  });
}

/**
 * 重置视觉参数到默认值
 * @param {Object} page - 页面实例
 */
function resetVisionParams(page) {
  try {
    const originalParams = page.data.originalDogVisionParams;
    const selectedBreed = page.data.breedName;
    
    if (!originalParams) {
      console.error('未找到原始参数数据');
      return;
    }
    
    // 创建重置后的参数对象
    let resetParams = { ...originalParams };
    
    // 哈士奇特殊处理：重置时也要保持特殊的亮度值
    if (selectedBreed && selectedBreed.includes('哈士奇')) {
      resetParams.brightness = 1.8; // 哈士奇的特殊亮度值
      console.log('哈士奇品种检测到，重置亮度为1.8');
    }
    
    // 更新页面数据
    page.setData({
      dogVisionParams: resetParams
    });
    
    // 如果有WebGL上下文，立即应用新参数
    if (page.processFrameWebGL && page._lastFrame) {
      page.processFrameWebGL(page._lastFrame);
    }
    
    console.log('视觉参数已重置为默认值:', resetParams);
    
    // 显示重置成功提示
    wx.showToast({
      title: '参数已重置',
      icon: 'success',
      duration: 1500
    });
    
  } catch (error) {
    console.error('重置视觉参数时出错:', error);
    wx.showToast({
      title: '重置失败',
      icon: 'error',
      duration: 1500
    });
  }
}

/**
 * 亮度增强滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onBrightnessChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.brightness': 1 + value / 50
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 对比度滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onContrastChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.contrast': 1 + value / 50
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 辨析度滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onResolutionChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.resolutionFactor': value / 100
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 抗锯齿强度滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onAntiAliasChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.antiAliasFactor': value / 100
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 运动敏感度滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onMotionSensitivityChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.motionSensitivity': value / 10
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 运动阈值滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onMotionThresholdChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.motionThreshold': value
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 运动物体大小阈值滑块变化处理
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function onMotionSizeThresholdChange(event, page) {
  const value = parseFloat(event.detail.value);
  
  page.setData({
    'dogVisionParams.motionSizeThreshold': value
  });
  
  // 立即应用新参数
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 获取参数的显示值
 * @param {number} value - 参数值
 * @param {string} type - 参数类型
 * @returns {string} 格式化后的显示值
 */
function getParamDisplayValue(value, type) {
  switch (type) {
    case 'brightness':
    case 'contrast':
      return value.toFixed(2) + 'x';
    case 'resolution':
    case 'antiAlias':
      return (value * 100).toFixed(0) + '%';
    case 'motionSensitivity':
      return value.toFixed(1);
    case 'motionThreshold':
    case 'motionSizeThreshold':
      return value.toFixed(0);
    default:
      return value.toString();
  }
}

/**
 * 获取滑块的当前值
 * @param {number} paramValue - 参数值
 * @param {string} type - 参数类型
 * @returns {number} 滑块值
 */
function getSliderValue(paramValue, type) {
  switch (type) {
    case 'brightness':
    case 'contrast':
      return (paramValue - 1) * 50;
    case 'resolution':
    case 'antiAlias':
      return paramValue * 100;
    case 'motionSensitivity':
      return paramValue * 10;
    case 'motionThreshold':
    case 'motionSizeThreshold':
      return paramValue;
    default:
      return paramValue;
  }
}

/**
 * 验证参数值是否在有效范围内
 * @param {number} value - 参数值
 * @param {string} type - 参数类型
 * @returns {boolean} 是否有效
 */
function validateParamValue(value, type) {
  switch (type) {
    case 'brightness':
    case 'contrast':
      return value >= 0.5 && value <= 3.0;
    case 'resolution':
    case 'antiAlias':
      return value >= 0.1 && value <= 1.0;
    case 'motionSensitivity':
      return value >= 0.1 && value <= 10.0;
    case 'motionThreshold':
      return value >= 1.0 && value <= 50.0;
    case 'motionSizeThreshold':
      return value >= 1.0 && value <= 100.0;
    default:
      return true;
  }
}

module.exports = {
  toggleVisionParams,
  resetVisionParams,
  onBrightnessChange,
  onContrastChange,
  onResolutionChange,
  onAntiAliasChange,
  onMotionSensitivityChange,
  onMotionThresholdChange,
  onMotionSizeThresholdChange,
  getParamDisplayValue,
  getSliderValue,
  validateParamValue
};
