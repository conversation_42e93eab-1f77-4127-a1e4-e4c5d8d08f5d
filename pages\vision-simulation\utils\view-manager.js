/**
 * 视图切换管理模块
 * 负责处理人类视角和宠物视角的切换逻辑
 * 从ui-controller.js中提取视图切换相关功能
 */

/**
 * 切换视角（人类/宠物）
 * @param {Object} page - 页面实例
 */
function toggleView(page) {
  const currentView = page.data.currentView;
  const newView = currentView === 'dog' ? 'human' : 'dog';
  
  page.setData({
    currentView: newView
  });
  
  console.log(`视角已切换: ${currentView} -> ${newView}`);
  
  // 显示切换提示
  const viewName = newView === 'dog' ? '宠物视角' : '人类视角';
  wx.showToast({
    title: `已切换到${viewName}`,
    icon: 'none',
    duration: 1500
  });
}

/**
 * 切换相机位置（前置/后置）
 * @param {Object} page - 页面实例
 */
function switchCameraPosition(page) {
  try {
    const currentPosition = page.data.cameraPosition;
    const newPosition = currentPosition === 'back' ? 'front' : 'back';
    
    console.log(`切换相机位置: ${currentPosition} -> ${newPosition}`);
    
    // 更新相机位置状态
    page.setData({
      cameraPosition: newPosition,
      cameraLoading: true,
      cameraError: false
    });
    
    // 重新初始化相机
    if (typeof page.initCamera === 'function') {
      page.initCamera();
    } else {
      console.error('initCamera方法不存在');
      page.setData({
        cameraLoading: false,
        cameraError: true,
        cameraErrorMsg: '相机初始化方法不存在'
      });
    }
    
    // 显示切换提示
    const positionName = newPosition === 'back' ? '后置相机' : '前置相机';
    wx.showToast({
      title: `切换到${positionName}`,
      icon: 'none',
      duration: 1500
    });
    
  } catch (error) {
    console.error('切换相机位置时出错:', error);
    
    page.setData({
      cameraLoading: false,
      cameraError: true,
      cameraErrorMsg: '相机切换失败: ' + error.message
    });
    
    wx.showToast({
      title: '相机切换失败',
      icon: 'error',
      duration: 2000
    });
  }
}

/**
 * 获取当前视图的显示名称
 * @param {string} currentView - 当前视图类型
 * @returns {string} 显示名称
 */
function getViewDisplayName(currentView) {
  switch (currentView) {
    case 'dog':
      return '宠物视角';
    case 'human':
      return '人类视角';
    default:
      return '未知视角';
  }
}

/**
 * 获取当前相机位置的显示名称
 * @param {string} cameraPosition - 相机位置
 * @returns {string} 显示名称
 */
function getCameraPositionDisplayName(cameraPosition) {
  switch (cameraPosition) {
    case 'back':
      return '后置相机';
    case 'front':
      return '前置相机';
    default:
      return '未知相机';
  }
}

/**
 * 检查是否可以切换视角
 * @param {Object} page - 页面实例
 * @returns {boolean} 是否可以切换
 */
function canToggleView(page) {
  // 检查相机是否正在加载
  if (page.data.cameraLoading) {
    console.log('相机正在加载，暂时无法切换视角');
    return false;
  }
  
  // 检查是否有相机错误
  if (page.data.cameraError) {
    console.log('相机出现错误，暂时无法切换视角');
    return false;
  }
  
  // 检查WebGL是否已初始化
  if (!page._webglInitialized) {
    console.log('WebGL未初始化，暂时无法切换视角');
    return false;
  }
  
  return true;
}

/**
 * 检查是否可以切换相机位置
 * @param {Object} page - 页面实例
 * @returns {boolean} 是否可以切换
 */
function canSwitchCameraPosition(page) {
  // 检查相机是否正在加载
  if (page.data.cameraLoading) {
    console.log('相机正在加载，暂时无法切换位置');
    return false;
  }
  
  // 检查是否在CAM模式
  if (page.data.camMode) {
    console.log('CAM模式下无法切换相机位置');
    return false;
  }
  
  return true;
}

/**
 * 安全切换视角（带检查）
 * @param {Object} page - 页面实例
 */
function safeToggleView(page) {
  if (!canToggleView(page)) {
    wx.showToast({
      title: '当前无法切换视角',
      icon: 'none',
      duration: 1500
    });
    return;
  }
  
  toggleView(page);
}

/**
 * 安全切换相机位置（带检查）
 * @param {Object} page - 页面实例
 */
function safeSwitchCameraPosition(page) {
  if (!canSwitchCameraPosition(page)) {
    wx.showToast({
      title: '当前无法切换相机',
      icon: 'none',
      duration: 1500
    });
    return;
  }
  
  switchCameraPosition(page);
}

/**
 * 重置视图状态
 * @param {Object} page - 页面实例
 */
function resetViewState(page) {
  page.setData({
    currentView: 'dog',
    cameraPosition: 'back',
    cameraLoading: false,
    cameraError: false,
    cameraErrorMsg: ''
  });
  
  console.log('视图状态已重置');
}

/**
 * 获取视图状态信息
 * @param {Object} page - 页面实例
 * @returns {Object} 状态信息
 */
function getViewState(page) {
  return {
    currentView: page.data.currentView,
    currentViewName: getViewDisplayName(page.data.currentView),
    cameraPosition: page.data.cameraPosition,
    cameraPositionName: getCameraPositionDisplayName(page.data.cameraPosition),
    cameraLoading: page.data.cameraLoading,
    cameraError: page.data.cameraError,
    cameraErrorMsg: page.data.cameraErrorMsg,
    canToggleView: canToggleView(page),
    canSwitchCamera: canSwitchCameraPosition(page)
  };
}

module.exports = {
  toggleView,
  switchCameraPosition,
  getViewDisplayName,
  getCameraPositionDisplayName,
  canToggleView,
  canSwitchCameraPosition,
  safeToggleView,
  safeSwitchCameraPosition,
  resetViewState,
  getViewState
};
