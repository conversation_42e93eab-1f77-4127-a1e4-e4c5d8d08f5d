/**
 * 视觉模式选择器相关样式模块
 * 包含圆形菜单、模式选择、级别徽章等特殊样式
 */

/* 视觉模式选择器容器 */
.vision-mode-selector-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1200;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(8px);
}

.vision-mode-selector-container.show {
  opacity: 1;
  visibility: visible;
}

/* 圆形菜单容器 */
.circular-menu-container {
  position: relative;
  width: 600rpx;
  height: 600rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 圆形菜单项 */
.circular-menu-item {
  position: absolute;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(40, 60, 100, 0.7));
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.circular-menu-item:active {
  transform: scale(0.95);
}

/* 不同级别的模式样式 */
.circular-menu-item.level-1 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(20, 60, 120, 0.7));
  border: 2px solid rgba(100, 140, 200, 0.3);
}

.circular-menu-item.level-2 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(20, 100, 60, 0.7));
  border: 2px solid rgba(100, 200, 140, 0.3);
}

.circular-menu-item.level-3 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(80, 40, 120, 0.7));
  border: 2px solid rgba(140, 100, 200, 0.3);
}

.circular-menu-item.level-4 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(150, 30, 30, 0.7));
  border: 2px solid rgba(200, 100, 100, 0.3);
}

/* 选中样式 */
.circular-menu-item.selected {
  transform: scale(1.03);
  box-shadow: 0 0 16rpx rgba(255, 255, 255, 0.4);
  border-width: 2px;
}

.circular-menu-item.level-1.selected {
  border-color: rgba(100, 140, 255, 0.8);
}

.circular-menu-item.level-2.selected {
  border-color: rgba(100, 255, 140, 0.8);
}

.circular-menu-item.level-3.selected {
  border-color: rgba(180, 140, 255, 0.8);
}

.circular-menu-item.level-4.selected {
  border-color: rgba(255, 100, 100, 0.8);
}

/* 模式图标 */
.mode-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 模式名称 */
.mode-name {
  font-size: 22rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
  margin-bottom: 6rpx;
}

/* 级别徽章 */
.level-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
}

.circular-menu-item.level-1 .level-badge {
  background: rgba(100, 140, 200, 0.4);
}

.circular-menu-item.level-2 .level-badge {
  background: rgba(100, 200, 140, 0.4);
}

.circular-menu-item.level-3 .level-badge {
  background: rgba(140, 100, 200, 0.4);
}

.circular-menu-item.level-4 .level-badge {
  background: rgba(200, 100, 100, 0.4);
}

/* 选中状态下的级别徽章样式增强 */
.circular-menu-item.selected .level-badge {
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.3);
}

.circular-menu-item.level-1.selected .level-badge {
  background: rgba(100, 140, 255, 0.6);
}

.circular-menu-item.level-2.selected .level-badge {
  background: rgba(100, 255, 140, 0.6);
}

.circular-menu-item.level-3.selected .level-badge {
  background: rgba(180, 140, 255, 0.6);
}

.circular-menu-item.level-4.selected .level-badge {
  background: rgba(255, 100, 100, 0.6);
}

/* 包含功能徽章 */
.includes-badge {
  position: absolute;
  bottom: 10rpx;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(0, 0, 0, 0.4);
  padding: 3rpx 10rpx;
  border-radius: 14rpx;
}

/* 中心说明区域 */
.center-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 1;
  pointer-events: none;
}

.center-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.center-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  max-width: 300rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 当前选择的模式提示 */
.mode-selected-tip {
  text-align: center;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 16rpx;
  padding: 12rpx 25rpx;
  margin: 0 auto;
  margin-bottom: 15rpx;
  max-width: 80%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

.mode-selected-tip text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 26rpx;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 为不同模式的提示添加不同的背景色调 */
.mode-tip-1 {
  background: rgba(20, 60, 120, 0.5);
  border-color: rgba(100, 140, 200, 0.3);
}

.mode-tip-2 {
  background: rgba(20, 100, 60, 0.5);
  border-color: rgba(100, 200, 140, 0.3);
}

.mode-tip-3 {
  background: rgba(80, 40, 120, 0.5);
  border-color: rgba(140, 100, 200, 0.3);
}

.mode-tip-4 {
  background: rgba(150, 30, 30, 0.5);
  border-color: rgba(200, 100, 100, 0.3);
}

/* 模式详情面板 */
.mode-detail-panel {
  position: absolute;
  bottom: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 500rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mode-detail-panel.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-10rpx);
}

.mode-detail-title {
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
  text-align: center;
}

.mode-detail-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  text-align: center;
  margin-bottom: 20rpx;
}

.mode-detail-includes {
  font-size: 22rpx;
  color: rgba(255, 193, 7, 0.9);
  text-align: center;
  background: rgba(255, 193, 7, 0.1);
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .circular-menu-container {
    width: 500rpx;
    height: 500rpx;
  }
  
  .circular-menu-item {
    width: 140rpx;
    height: 140rpx;
  }
  
  .mode-icon {
    font-size: 40rpx;
  }
  
  .mode-name {
    font-size: 20rpx;
  }
  
  .level-badge {
    width: 36rpx;
    height: 36rpx;
    font-size: 18rpx;
  }
  
  .center-title {
    font-size: 28rpx;
  }
  
  .center-desc {
    font-size: 22rpx;
    max-width: 250rpx;
  }
  
  .mode-detail-panel {
    width: 85%;
    padding: 24rpx;
    bottom: 60rpx;
  }
  
  .mode-detail-title {
    font-size: 26rpx;
  }
  
  .mode-detail-desc {
    font-size: 22rpx;
  }
  
  .mode-detail-includes {
    font-size: 20rpx;
    padding: 10rpx 16rpx;
  }
  
  .close-btn {
    top: 30rpx;
    right: 30rpx;
    width: 50rpx;
    height: 50rpx;
    font-size: 28rpx;
  }
}
