/**
 * 提示和动画相关样式模块
 * 包含各种提示框、动画效果、过渡效果等样式
 */

/* 运动视觉提示样式 */
.motion-vision-tip {
  position: absolute;
  top: 120rpx; /* 从60rpx修改为120rpx，增加与顶部的距离 */
  left: 50%;
  transform: translateX(-50%) translateY(-20rpx);
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  width: 80%;
  max-width: 600rpx;
}

.motion-tip-show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
  animation: fadeInDown 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.motion-tip-content {
  background: linear-gradient(135deg, rgba(255, 138, 91, 0.9), rgba(255, 100, 100, 0.9));
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 20rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.motion-tip-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5rpx;
  line-height: 1.4;
}

/* 光照模式提示样式 */
.light-mode-tip {
  position: absolute;
  top: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 900;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
}

.light-mode-tip.show {
  opacity: 1;
  visibility: visible;
  animation: tipSlideDown 0.4s ease;
}

.light-tip-content {
  background: rgba(255, 193, 7, 0.9);
  backdrop-filter: blur(10px);
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
  border: 1px solid rgba(255, 193, 7, 0.5);
  min-width: 200rpx;
  text-align: center;
}

.bright-light-tip .light-tip-content {
  background: rgba(255, 165, 0, 0.9);
  box-shadow: 0 4px 16px rgba(255, 165, 0, 0.3);
  border-color: rgba(255, 165, 0, 0.5);
}

.night-vision-tip .light-tip-content {
  background: rgba(138, 43, 226, 0.9);
  box-shadow: 0 4px 16px rgba(138, 43, 226, 0.3);
  border-color: rgba(138, 43, 226, 0.5);
}

.light-tip-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
}

.light-tip-text {
  font-size: 24rpx;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  line-height: 1.3;
}

/* 视觉模式选择引导提示 */
.vision-select-guide {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(20rpx);
  margin-bottom: 16rpx;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 950;
  pointer-events: none;
}

.vision-select-guide.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.vision-select-guide .guide-content {
  background: linear-gradient(135deg, rgba(255, 138, 91, 0.9), rgba(255, 100, 100, 0.9));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 14rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  text-align: center;
}

.vision-select-guide .guide-content text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5rpx;
}

.vision-select-guide .guide-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid rgba(255, 100, 100, 0.9);
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 视觉模式按钮容器 */
.vision-mode-btn-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 通用动画效果 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes tipSlideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-6rpx); }
  100% { transform: translateY(0); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 成功提示动画 */
.success-tip {
  animation: successPop 0.6s ease;
}

@keyframes successPop {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 错误提示动画 */
.error-tip {
  animation: errorShake 0.5s ease;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-8px); }
  75% { transform: translateX(8px); }
}

/* 警告提示动画 */
.warning-tip {
  animation: warningBounce 0.8s ease;
}

@keyframes warningBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 渐变背景动画 */
.gradient-bg {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 呼吸灯效果 */
.breathing-light {
  animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .motion-vision-tip {
    top: 100rpx;
    width: 85%;
    max-width: 500rpx;
  }
  
  .motion-tip-content {
    padding: 16rpx 24rpx;
  }
  
  .motion-tip-text {
    font-size: 26rpx;
  }
  
  .light-mode-tip {
    top: 70rpx;
  }
  
  .light-tip-content {
    padding: 14rpx 20rpx;
    min-width: 180rpx;
  }
  
  .light-tip-icon {
    font-size: 26rpx;
  }
  
  .light-tip-text {
    font-size: 22rpx;
  }
  
  .vision-select-guide .guide-content {
    padding: 12rpx 20rpx;
  }
  
  .vision-select-guide .guide-content text {
    font-size: 26rpx;
  }
}
