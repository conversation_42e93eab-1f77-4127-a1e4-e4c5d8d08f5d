/**
 * 控制面板相关样式模块
 * 包含iOS控制栏、按钮、滑块等控制元素样式
 */

/* iOS风格控制栏 */
.ios-control-bar {
  position: fixed;
  right: 20rpx;
  bottom: 200rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom, 20rpx);
  transition: all 0.3s ease;
}

/* 当分析区域展开时，调整控制栏位置 */
.analysis-container.expanded ~ .main-content .ios-control-bar {
  bottom: 50vh;
  right: 30vw;
}

/* iOS按钮基础样式 */
.ios-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.ios-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ios-btn:active {
  transform: scale(0.85);
  background: rgba(255, 138, 91, 0.3);
  border-color: rgba(255, 138, 91, 0.5);
}

.ios-btn:active::before {
  opacity: 1;
}

/* 特殊按钮样式 */
.ios-btn.active {
  background: rgba(255, 138, 91, 0.2);
  border-color: rgba(255, 138, 91, 0.4);
  color: rgba(255, 138, 91, 0.9);
  animation: activePulse 2s ease-in-out infinite;
}

@keyframes activePulse {
  0%, 100% { 
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(255, 138, 91, 0.4); 
  }
  50% { 
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3), 0 0 0 8rpx rgba(255, 138, 91, 0); 
  }
}

/* 按钮标签 */
.ios-btn-label {
  position: absolute;
  right: 120rpx;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.9);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ios-btn:hover .ios-btn-label,
.ios-btn:active .ios-btn-label {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(-10rpx);
}

/* 视觉参数调整面板 */
.vision-params-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(40, 60, 90, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40rpx 30rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom, 0));
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 200;
  max-height: 60vh;
  overflow-y: auto;
}

.vision-params-panel.show {
  transform: translateY(0);
}

.light-theme-content .vision-params-panel {
  background: rgba(250, 250, 250, 0.95);
  border-top-color: rgba(0, 0, 0, 0.1);
}

/* 参数面板标题 */
.params-panel-title {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40rpx;
  text-align: center;
  letter-spacing: 0.5rpx;
}

.light-theme-content .params-panel-title {
  color: rgba(0, 0, 0, 0.9);
}

/* 参数组 */
.param-group {
  margin-bottom: 50rpx;
}

.param-group:last-child {
  margin-bottom: 0;
}

.param-label {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.light-theme-content .param-label {
  color: rgba(0, 0, 0, 0.8);
}

.param-value {
  font-size: 24rpx;
  color: rgba(255, 138, 91, 0.9);
  font-weight: 600;
}

/* 滑块样式 */
.param-slider {
  margin-bottom: 10rpx;
}

.param-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
  margin-top: 10rpx;
}

.light-theme-content .param-desc {
  color: rgba(0, 0, 0, 0.6);
}

/* 参数面板操作按钮 */
.params-panel-actions {
  display: flex;
  gap: 30rpx;
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme-content .params-panel-actions {
  border-top-color: rgba(0, 0, 0, 0.1);
}

.params-action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.params-action-btn.reset {
  background: rgba(220, 20, 60, 0.2);
  border-color: rgba(220, 20, 60, 0.4);
  color: rgba(220, 20, 60, 0.9);
}

.params-action-btn.reset:active {
  background: rgba(220, 20, 60, 0.3);
  transform: scale(0.95);
}

.params-action-btn.close {
  background: rgba(70, 130, 180, 0.2);
  border-color: rgba(70, 130, 180, 0.4);
  color: rgba(70, 130, 180, 0.9);
}

.params-action-btn.close:active {
  background: rgba(70, 130, 180, 0.3);
  transform: scale(0.95);
}

/* 切换开关样式 */
.toggle-switch {
  position: relative;
  width: 100rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.toggle-switch.active {
  background: rgba(255, 138, 91, 0.3);
  border-color: rgba(255, 138, 91, 0.5);
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.toggle-switch.active::after {
  transform: translateX(40rpx);
  background: rgba(255, 138, 91, 0.9);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .ios-control-bar {
    right: 16rpx;
    bottom: 180rpx;
    gap: 16rpx;
  }
  
  .ios-btn {
    width: 80rpx;
    height: 80rpx;
    font-size: 32rpx;
  }
  
  .ios-btn-label {
    right: 100rpx;
    font-size: 22rpx;
    padding: 10rpx 16rpx;
  }
  
  .vision-params-panel {
    padding: 30rpx 20rpx;
    max-height: 50vh;
  }
  
  .params-panel-title {
    font-size: 32rpx;
    margin-bottom: 30rpx;
  }
  
  .param-group {
    margin-bottom: 40rpx;
  }
  
  .param-label {
    font-size: 26rpx;
  }
  
  .param-desc {
    font-size: 20rpx;
  }
  
  .params-action-btn {
    height: 70rpx;
    font-size: 26rpx;
  }
}
