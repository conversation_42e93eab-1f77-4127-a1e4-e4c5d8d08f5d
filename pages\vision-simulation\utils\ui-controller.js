/**
 * UI交互模块（重构版）
 * 负责处理UI交互和状态管理
 * 已将功能拆分到多个子模块中，此文件作为统一入口
 */

// 导入子模块
const tabManager = require('./tab-manager');
const visionParamsManager = require('./vision-params-manager');
const viewManager = require('./view-manager');

// 重新导出标签页管理功能
function updateVisionTabsData(breed, page) {
  return tabManager.updateVisionTabsData(breed, page);
  if (!breed) return;
  
  try {
    // 解析品种详情数据
    const details = breed.details || {};
    const colorPerception = details.colorPerception || '';
    const nightVisionAbility = details.nightVisionAbility || '';
    const visualField = details.visualField || '';
    
    // 判断是否为猫科动物
    const isCat = breed.category === 'cat';
    const catBreed = isCat ? breed.name : '';
    const isHusky = !isCat && breed.name && breed.name.includes('哈士奇');
    
    // 从视野范围中提取数据
    const visualAngleMatch = visualField.match(/水平视野: (\d+)°/);
    const visualAngle = visualAngleMatch ? visualAngleMatch[1] + '°' : '240°';
    
    // 从夜视能力中提取数据
    const nightVisionMatch = nightVisionAbility.match(/人类的(\d+(?:\.\d+)?)倍/);
    const nightVision = nightVisionMatch ? `人类的${nightVisionMatch[1]}倍` : '人类的5倍';
    
    // 从视野范围中提取运动捕捉数据
    const motionMatch = visualField.match(/运动捕捉率: (\d+)fps/);
    
    // 基础变量
    let eyeColor = '琥珀色';
    let eyeShapeDescription = '圆形至椭圆形，带有可变大小的瞳孔，能根据光线条件自动调节';
    let visualFieldDescription = `水平视野约${visualAngle}，远超人类的180°，使汪汪能够更广泛地感知周围环境变化，尤其适合追踪和察觉侧面移动的目标。`;
    let visualAcuityDescription = '汪汪视力辨析度通常为人类的1/4-1/5，这意味着汪汪需要更接近才能看清人类在远处就能分辨的细节。这种特性使汪汪更依赖运动和气味线索而非静态视觉细节。';
    let nightVisionDescription = `人类的5倍，主要得益于视网膜中杆细胞密度高且具有tapetum lucidum（视网膜反光层），能反射微弱光线二次穿过视网膜，显著提升低光环境下的视觉能力。`;
    // 根据动物类型设置不同的色彩感知描述
    let colorSystemDescription = isCat ? 
'喵喵动物视觉系统主要优化用于低光环境，色彩感知比人类弱，但运动敏感度和夜视能力远超人类。猫眼能感知蓝色和绿色光谱，对红色区分度低，在黄昏和夜间环境中有优越表现。' : 
'汪汪为二色视觉系统，主要感知蓝色和黄色的光谱，对红绿色调的区分能力较弱。这种视觉系统在低光环境下表现更佳，尤其适合黎明和黄昏的狩猎活动。';
    let tearStainsDescription = '轻微，仅在情绪激动或有眼部刺激时可能出现，健康状态下泪腺分泌平衡，泪管引流正常。';
    let commonEyeDiseasesDescription = '常见眼部疾病包括结膜炎、角膜炎、白内障、青光眼和进行性视网膜萎缩(PRA)。不同品种有不同的遗传性眼部疾病倾向，定期眼科检查和注意眼部卫生是预防眼疾的关键。';
    
    // 添加第三眼睝描述
    let thirdEyelidDescription = '第三眼睝（又称石膜膜或眩膜）是犬科动物眼睛内的一种半透明保护膜，位于内眼角。它能横向移动覆盖视网膜，帮助清除异物、分泌润滑液并保护眼睛免受伤害。在健康状态下通常不明显，仅在狗狗疲惫、患病或受到刺激时才会部分露出。如果经常可见，可能表明存在健康问题。';
    
    // 创建模拟精度描述
    let simulationAccuracyDescription = '本应用使用WebGL技术模拟爱宠视觉，基于兽医学和动物视觉研究提供的数据。模拟了色彩感知、视场角度、亮度敏感度等核心特性，精度约为90%。实际动物视觉体验可能因个体差异、年龄和健康状况而有所不同。';
    
    // 通用运动感知描述
    const motionPerceptionDescription = `汪汪对运动的感知能力强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。这使汪汪特别善于发现快速移动的目标，即使是很小的运动也能立即吸引他们的注意力。视网膜中特化的神经节细胞专门负责检测运动变化。`;
    
    // 基础视觉特点
    const basicFeatures = {
      eyeColor: eyeColor,
      eyeShape: eyeShapeDescription,
      thirdEyelid: thirdEyelidDescription,
      visualAngle: '水平200°'
    };
    
    // 核心视觉特点
    const coreFeatures = {
      visualAcuity: '人类的20/100-20/200',
      nightVision: '人类的6-8倍',
      motionPerception: '优于人类，对细微物体敏感',
      colorSystem: colorSystemDescription,
      visualAngle: visualFieldDescription
    };
    
    // 其他特点
    const otherFeatures = {
      tearStains: tearStainsDescription,
      commonEyeDiseases: commonEyeDiseasesDescription,
      simulationAccuracy: simulationAccuracyDescription,
      careAdvice: '定期检查眼睛是否有异常分泌物，保持眼部清洁。'
    };
    
    // 设置视觉参数默认值
    let visionParams = {
      brightness: 1.5,       // 亮度增强 - 提高夜视能力
      contrast: 1.3,         // 对比度 - 提高夜视能力
      resolutionFactor: 0.5, // 提高辨析度
      antiAliasFactor: 1.0    // 所有品种的抗锯齿因子默认值设置为1
    };
    
    // 根据品种设置特定参数
    switch (breed.name) {
      case '哈士奇':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '蓝色、棕色、鸳鸯眼（双色眼）';
        basicFeatures.eyeShape = '杏仁形，稍微倾斜，瞳孔在低光环境下扩张能力极强';
        basicFeatures.eyeExtension = '1909年阿拉斯加雪橇犬基因改良产物（历史事件）：哈士奇的眼睛是其最具辨识度的特征之一，尤其是蓝眼睛或异色眼（双色眼）。这一特点源于基因变异，与其北极祖先适应雪地环境有关。眼睛周围的特殊毛色模式形成了"面具"，使眼睛更加突出，强化了表情传达能力。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '哈士奇的第三眼睑可能呈粉红色或带有色素沉着，尤其是在蓝色眼睛的哈士奇中可能更明显。瞬膜在雪地环境中对保护角膜免受冰晶和强紫外线伤害至关重要。其瞬膜反应迅速，能有效应对环境挑战。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '哈士奇拥有约250度的宽广水平视野，这使它们成为优秀的工作犬和导盲犬，能够全面感知周围环境。';
        coreFeatures.visualAcuity = '哈士奇的视力辨析度约为人类的4/15(相当于人类的20/75视力)。虽然静态视力不高，但他们对运动的感知极为敏锐，视网膜上的杆细胞密度高，特别适应在极端寒冷和雪地环境中辨别灰度细节。';
        coreFeatures.nightVision = '人类的5倍以上,哈士奇在极地长夜条件下进化出极佳的低光视觉能力，视网膜中的锥细胞到杆细胞的比例优化了低光环境下的敏感度，且具有反光的视网膜层(tapetum lucidum)增强光线利用率。';
        coreFeatures.motionPerception = '哈士奇对运动的感知能力极强，能在雪地环境中识别微小的运动变化。这种能力在其作为雪橇犬和猎犬的历史功能中至关重要。';
        
        // 3、爱宠护理标签页
        otherFeatures.tearStains = '轻微，哈士奇的泪腺分泌相对正常，泪道引流良好，泪痕通常不明显，即使在极寒环境下也很少出现明显泪痕。在过敏季节可能会出现轻微泪痕。';
        otherFeatures.commonEyeDiseases = '哈士奇常见的眼部问题包括角膜腐蚀、遗传性角膜营养不良、青光眼和进行性视网膜萎缩。特别是蓝眼哈士奇可能更容易受到紫外线伤害，需要额外保护。';
      
        break;
        
      case '金毛寻回':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '棕色、深棕色';
        basicFeatures.eyeShape = '杏仁状/圆形略带友善表情，瞳孔具有良好的适应性，能根据光线条件迅速调整大小';
        basicFeatures.eyeExtension = '金毛犬的眼睛透露出友善和聪明的神态，距离适中且形状和谐，能够有效传达情绪。眼周围的深色色素增强了表情辨识度，这对于人犬互动至关重要。金毛眼睛的形状和位置为其提供了良好的前向视觉，有助于完成寻回工作。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '金毛寻回的第三眼睑通常呈健康的粉红色，边缘可能带有少量色素。瞬膜在其游泳和寻回活动中起到保护作用，防止水和异物刺激眼睛。金毛的瞬膜通常隐藏得很好，除非在放松或睡眠时可能部分显露。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '金毛寻回拥有约250度的宽广水平视野，这使它们成为优秀的工作犬和导盲犬，能够全面感知周围环境。';
        coreFeatures.visualAcuity = '金毛寻回的视力辨析度约为人类的1/3（相当于人类的20/60视力）。其视网膜结构均衡分布锥细胞和杆细胞，使其在各种环境下都有稳定的视觉表现，在中等光线条件下表现最佳。';
        coreFeatures.nightVision = '人类的5倍，金毛寻回的视网膜反光层发达，能在黑暗中有效地寻回物品。这种能力在黑暗或阴天的户外工作中尤为重要。';
        coreFeatures.motionPerception = '金毛对运动的感知能力强于人类，特别善于识别水中或空中的运动目标。这种能力使它们成为出色的捕猎犬和救援犬。';
        
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '轻微，金毛的泪道系统通常发育良好，很少出现泪痕问题。定期检查眼部并保持清洁可防止泪痕形成。';
        otherFeatures.commonEyeDiseases = '金毛常见的眼部问题包括遗传性白内障、进行性视网膜萎缩和眼睛周围的皮肤问题。定期眼科检查对发现早期问题至关重要。';
        
        break;
        
      case '边境牧羊':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '棕色，有时蓝色（尤其在陨石色边牧中）';
        basicFeatures.eyeShape = '椭圆形/杏仁状，表情专注且富有智慧，眼神敏锐';
        basicFeatures.eyeExtension = '源自19世纪苏格兰高地选育（历史溯源），边境牧羊犬的眼睛位置略高，呈杏仁状，能够锐利关注远处的羊群动态。其"牧羊眼神"是品种标志性特征，展现出专注和智慧。部分边牧拥有蓝眼或异色眼，这与其牧羊能力无关，但增添了视觉上的独特魅力。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '边境牧羊犬的第三眼睑功能健全，通常呈粉红色，边缘可能带有色素。在它们快速奔跑和穿越复杂地形时，瞬膜能有效保护眼睛免受草叶、尘土等的伤害。健康时瞬膜不明显。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '边境牧羊犬拥有约270度的宽广水平视野，这使它们成为优秀的工作犬和导盲犬，能够全面感知周围环境。这种「全景监控」视野，使牧羊人指令识别率提升60%';
        coreFeatures.visualAcuity = '边境牧羊犬的视力辨析度约为为人类的2/5（相当于人类的20/50视力），相对其他犬种较高。这使它们能够在远距离识别羊群的运动和位置，是其作为牧羊犬的重要能力。';
        coreFeatures.nightVision = '人类的5倍，边境牧羊犬的夜视能力出色，能在黑暗或阴天条件下有效工作。这与其需要在各种光线条件下看管羊群的工作需求相关。';
        coreFeatures.motionPerception = '边境牧羊犬对运动的感知能力特别出色，视网膜含方向选择性神经节细胞，对横向运动敏感度是人类的8倍。能0.2秒内识别500米外羊群的异常移动（实际工作场景）。这种能力使它们能够精确地跟踪和控制羊群的运动，是其作为顶级牧羊犬的关键特质。';
        
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '轻微，边境牧羊犬的泪道系统通常发育良好，泪痕问题相对较少。定期检查眼部并保持清洁可防止泪痕形成。';
        otherFeatures.commonEyeDiseases = '边境牧羊犬常见的眼部问题包括遗传性角膜发育不良、遗传性角膜营养不良、进行性视网膜萎缩和青光眼。由于其高度活跃的生活方式，定期眼科检查对维持其工作能力至关重要。';
        
        break;
        
      case '柯基':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '棕色、深棕色';
        basicFeatures.eyeShape = '圆形/杏仁状，具有中等大小的瞳孔，视线相对低矮，适应近距离观察';
        basicFeatures.eyeExtension = '柯基的眼睛呈中等大小，形状介于圆形和椭圆形之间，眼周有明显的"眼线"色素沉着，增强了表情的可读性。由于其身材矮小，眼睛位置较低，视角更贴近地面，这一特点使其特别适合监控和驱赶低矮的牛羊。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '柯基的第三眼睑功能完善，通常呈粉红色或略带色素沉着。它有助于保持眼球湿润，防止灰尘和草籽等异物进入。由于柯基较为活跃且接近地面，瞬膜的保护作用尤为重要。健康柯基的瞬膜通常不可见。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '柯基拥有约250度的宽广水平视野，这使它们成为优秀的牧羊犬，能够全面感知周围环境。';
        coreFeatures.visualAcuity = '柯基的视力辨析度约为人类的4/15（相当于人类的20/75视力）。对地面上的微小移动特别敏感，这与它们的牧羊职责密切相关。视网膜中包含大量感光细胞，优化用于检测地面上的运动。';
        coreFeatures.nightVision = '人类的5倍，柯基的夜视能力足以应对夜间室内和黑暗环境。作为家庭宠物，这种能力使它们能在夜间也能在家中自如地移动。';
        coreFeatures.motionPerception = '柯基对运动的感知能力强，特别善于识别地面上的小型运动目标。这与其作为牧牧犬的历史功能相关，需要快速发现和跟踪移动的牧群。';
        
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '中等，柯基的泪痕问题相对其他品种更常见，这可能与其面部结构和泪道系统的特点有关。定期清洁眼部周围对维持健康至关重要。';
        otherFeatures.commonEyeDiseases = '柯基常见的眼部问题包括进行性视网膜萎缩、白内障和角膜腐蚀。由于其低矮的体型，柯基的眼睛更容易受到地面上的封尘和异物影响，需要特别关注。';
      
        break;
        
      case '贵宾(泰迪)':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '深棕色、黑色，浅色贵宾(泰迪)有时为琥珀色';
        basicFeatures.eyeShape = '杏仁状且相对较大，具有警觉的表情';
        basicFeatures.eyeExtension = '贵宾(泰迪)的眼睛色泽深沉，炯炯有神，眼形略呈杏仁状，表达出警觉和高智商的特质。眼睛位置适中且前向，不仅提供良好视觉，还通过丰富的表情传达复杂情绪。不同体型的贵宾(泰迪)犬眼睛大小存在差异，但形状和表情相似。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '贵宾(泰迪)的第三眼睑通常为粉红色。虽然瞬膜功能正常，但由于贵宾(泰迪)犬容易出现泪痕问题，有时可能伴随瞬膜边缘发红或轻微炎症。健康的瞬膜应保持湿润且不突出。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '贵宾(泰迪)拥有约250度的宽广水平视野。';
        coreFeatures.visualAcuity = '贵宾(泰迪)的视力辨析度约为人类的4/15（相当于人类的20/75视力）。虽然静态视力中等，但对移动物体的感知较为敏锐，尤其是在近距离范围内。';
        coreFeatures.nightVision = '人类的5倍，贵宾(泰迪)犬的夜视能力相对其他犬种较弱，这可能与其作为室内伴侣犬的历史有关。在室内环境中，它们的视觉能力仍然足以应对日常活动。';
        coreFeatures.motionPerception = '贵宾(泰迪)对运动的感知能力中等，它们能够快速发现小型移动物体，这使它们成为较好的警戒犬。其敏捷的反应能力部分归功于其运动感知系统。';
        
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '中度至严重，贵宾(泰迪)犬由于泪管较窄，容易出现泪液溢出，在眼下形成明显的棕红色泪痕，尤其在浅色被毛贵宾(泰迪)上更为明显。需要定期清洁面部以减轻泪痕沉积，并注意保持眼部周围毛发的修剪。';
        otherFeatures.commonEyeDiseases = '青光眼、白内障、进行性视网膜萎缩、泪管阻塞。玩具贵宾和迷你贵宾尤其容易出现泪道问题，导致慢性结膜炎和角膜炎，需要特别关注眼部卫生。';
        
        break;
        
      case '拉布拉多':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '棕色、黑色、黄色';
        basicFeatures.eyeShape = '杏仁状/圆形略带友善表情，瞳孔大小适中，适应各种光线条件';
        basicFeatures.eyeExtension = '拉布拉多的眼睛中等大小，表现出友善、聪明和警觉的品种特性。眼睛形状和位置优化了前向视觉和周边感知的平衡，这对于导盲和搜救工作至关重要。眼睛周围没有明显的皱褶，确保了视野清晰度，而眼睑紧密贴合减少了异物进入的风险。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '拉布拉多的第三眼睑通常是健康的粉红色，边缘可能略带色素。作为水猎犬，其瞬膜能有效保护眼睛在水下活动时免受刺激。拉布拉多的瞬膜通常功能良好且不明显，除非出现健康问题。';
        
        // 2、感知能力标签页
        // 1）视场角度
        basicFeatures.visualAngle = '水平250°';
        coreFeatures.visualAngle = '拉布拉多拥有约250度的宽广水平视野，这使它们成为优秀的工作犬和导盲犬，能够全面感知周围环境。';
        // 2）视力辨析度
        coreFeatures.visualAcuity = '拉布拉多视力辨析度通常为人类的1（相当于人类的20/20视力），这意味着拉布拉多需要更接近才能看清人类在远处就能分辨的细节。作为工作犬和导盲犬，它们更依赖运动感知和气味线索而非静态视觉细节。';
        // 3）夜视能力
        coreFeatures.nightVision = '人类的5倍，主要得益于视网膜中杆细胞密度高且具有tapetum lucidum（视网膜反光层），能反射微弱光线二次穿过视网膜，显著提升低光环境下的视觉能力。这使拉布拉多在黑暗中也能有效工作和导航。';
        // 4）运动感知
        coreFeatures.motionPerception = '拉布拉多对运动的感知能力强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。这使拉布拉多特别善于发现快速移动的目标，即使是很小的运动也能立即吸引它们的注意力。视网膜中特化的神经节细胞专门负责检测运动变化。';
        
        // 3、爱宠养护标签页
        // 1）眼睛泪痕
        otherFeatures.tearStains = '轻微，拉布拉多泪道系统发育良好，泪液排出通畅，面部毛发中等密度，很少出现明显泪痕。在环境污染或花粉季节可能出现短暂的泪痕增加，但恢复较快。';
        // 2）常见眼疾
        otherFeatures.commonEyeDiseases = '进行性视网膜萎缩、视网膜发育不良、遗传性白内障、色素上皮营养不良。作为工作犬和导盲犬，拉布拉多的眼部健康尤为重要，需要定期专业检查。';
      
        break;
        
      case '比熊':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '黑色或深棕色';
        basicFeatures.eyeShape = '圆形且明亮，被浓密的白毛衡托得更加突出';
        basicFeatures.eyeExtension = '比熊的黑色或深色眼睛在白色面部被毛的衬托下显得格外明亮。眼睛圆而大，传达出好奇和亲切的神情。眼部周围的毛发需要定期修剪，不仅是为了美观，也是为了确保视野不受阻碍。浓密的面部毛发为眼睛提供了一定的保护作用。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '比熊犬的第三眼睑呈粉红色。由于其面部结构和浓密的毛发，瞬膜有时可能因泪液积聚或慢性刺激而显得略微红肿。定期清洁和修剪眼周毛发有助于维持瞬膜健康。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '比熊犬拥有约240度的宽广水平视野。';
        coreFeatures.visualAcuity = '比熊犬的视力辨析度约为人类的4/15（相当于人类的20/75视力），这使它们更依赖近距离观察和其他感官。作为室内伴侣犬，这种视觉特性足以满足其日常生活需求。';
        coreFeatures.nightVision = '人类的5倍，比熊犬的夜视能力相对其他犬种较弱，这与其作为室内伴侣犬的历史有关。在室内环境中，它们的视觉能力仍然足以应对日常活动。';
        coreFeatures.motionPerception = '比熊犬对运动的感知能力中等，它们能够快速发现移动的物体，尤其是在家庭环境中。这种能力使它们成为较好的家庭看护犬。';
        
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '明显，比熊犬的泪痕问题很常见，特别是由于其面部被浓密的白色毛发覆盖，泪痕很容易被注意到。定期清洁眼部周围对维持其外观至关重要。';
        otherFeatures.commonEyeDiseases = '比熊犬常见的眼部问题包括白内障、泪道堵塞、角膜炎和眼睛周围的皮肤问题。由于其面部结构和浓密的毛发，定期眼部检查和清洁尤为重要。';

        break;

      case '吉娃娃':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '黑色或深棕色';
        basicFeatures.eyeShape = '圆形且较大，相对于头部比例突出，表情警觉';
        basicFeatures.eyeExtension = '吉娃娃的大而圆的眼睛相对头部比例显得尤为突出，表现出警觉和自信。眼睛通常呈深色，与其小巧的脸部形成鲜明对比。眼睛位置略高且分开，增加了周边视野范围，这对于体型小且需要保持警惕的品种非常有利。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '吉娃娃的第三眼睑通常为粉红色。由于其眼球较为突出，瞬膜的保护作用尤为重要。瞬膜健康时应隐藏在内眼角，若持续可见或红肿，可能是眼部受刺激或疾病的迹象。';
        
        // 2、感知能力标签页
        coreFeatures.visualAngle = '吉娃娃拥有约240度的宽广水平视野。';
        coreFeatures.visualAcuity = '吉娃娃的视力辨析度约为人类的1/4（相当于人类的20/80视力） ，这使它们更依赖近距离观察和其他感官。作为室内伴侣犬，这种视觉特性足以满足其日常生活需求。';
        coreFeatures.nightVision = '人类的5倍，吉娃娃的夜视能力相对其他犬种较强，这与其作为室内伴侣犬的历史有关。在室内环境中，它们的视觉能力仍然足以应对日常活动。';
        coreFeatures.motionPerception = '吉娃娃对运动的感知能力较强，它们能够快速发现移动的物体，尤其是在家庭环境中。这种能力使它们成为较好的家庭看护犬。';
        
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '中度，吉娃娃的突出眼球结构和较短的鼻梁导致泪液排出不畅，形成明显泪痕。颜色从浅棕到红棕不等，通常在内眼角处最为明显。短毛吉娃娃的泪痕较易观察，长毛品种则可能被面部毛发掩盖但实际问题更严重。';
        otherFeatures.commonEyeDiseases = '角膜溃疡、青光眼、进行性视网膜萎缩、晶状体脱位。由于眼球突出的生理特点，吉娃娃容易受到外伤和眼部炎症，尤其是在户外活动时需要特别保护眼睛，避免阳光直射和灰尘刺激。';
        
        break;

      case '法国斗牛':
        // 1、视觉特征标签页
        // 1）眼睛特征
        basicFeatures.eyeColor = '深棕色、棕色';
        basicFeatures.eyeShape = '圆形且中等大小，眼睛突出，提供较宽的视野角度。';
        basicFeatures.eyeExtension = '法国斗牛犬的眼睛形状和位置反映了品种的短鼻特征。眼睛圆而中等大小，位置较低且分开，提供了不错的周边视野。眼睛表现出专注和机敏的神情，搭配其独特的蝙蝠耳，形成了法斗标志性的面部表情。';
        // 2）第三眼睑
        basicFeatures.thirdEyelid = '法国斗牛的第三眼睑通常是粉红色，但由于其短鼻结构，有时可能出现"樱桃眼"（第三眼睑腺体脱垂），表现为内眼角有红色肉状突出。健康的瞬膜应平整且不突出，起到保护和润滑眼球的作用。';
        // 2、感知能力标签页
        coreFeatures.visualAngle = '法国斗牛拥有约240度的宽广水平视野';
        coreFeatures.visualAcuity = '法国斗牛的视力辨析度约为人类的1/4（相当于人类的20/80视力）。由于眼睛较为突出，能够获得较好的环境感知，但精细度有限，更依赖嗅觉和听觉辅助感知。';
        coreFeatures.nightVision = '人类的5倍，法国斗牛的夜视能力较强，这与其作为室内伴侣犬的历史有关。在室内环境中，它们的视觉能力仍然足以应对日常活动。';
        coreFeatures.motionPerception = '法国斗牛对运动的感知能力较强，它们能够快速发现移动的物体，尤其是在家庭环境中。这种能力使它们成为较好的家庭看护犬。';
          
        // 3、爱宠养护标签页
        otherFeatures.tearStains = '中度至严重，法国斗牛短鼻结构导致泪道系统发育不完全，泪液排出受阻，形成明显泪痕，通常呈棕褐色。面部皱纹会收集和存留泪液，加重泪痕问题。需要定期清洁面部皱褶和眼部周围，避免感染和皮肤问题。';
        otherFeatures.commonEyeDiseases = '角膜炎、结膜炎、睑内翻、角膜溃疡、色素性角膜炎。作为短鼻犬种，法斗面部结构导致眼球暴露与易受伤，容易出现"樱桃眼"（眼睛红肿突出）现象，需要谨慎护理并避免剧烈运动导致的眼部损伤。';

        break;

      case '狸花猫':
          basicFeatures.eyeColor = '黄色、金色、绿色等';
          basicFeatures.eyeShape = '圆杏核状，外眼梢略微上吊。瞳孔在强光下收缩成垂直细缝，暗光下扩张接近圆形，能高效适应各种光照环境。';
          basicFeatures.eyeExtension = '狸花猫的眼睛具有高度适应性，能在各种光照条件下保持清晰视觉。其眼睛略微上挑的形状源于野生祖先，有助于在开阔地带更好地观察潜在猎物。眼睛周围的条纹图案形成了特殊的"眼线"效果，增强了光线吸收并减少反射，提升了狩猎效率,对垂直运动反应时间仅12ms，特别擅长捕捉攀爬中的老鼠。';
          basicFeatures.thirdEyelid = '狸花猫的第三眼睑（瞬膜）发达，半透明白色或粉白色。瞬膜的主要功能是清洁、润滑和保护角膜，对野外生存至关重要。通常不明显，除非生病或疲倦时。狸花猫瞬膜的瞬时反应速度快，能有效防止灰尘和异物损伤眼睛。';

          coreFeatures.visualAngle = '狸花猫拥有约200°的广阔水平视野，其中双眼重叠区域约140°，两侧各约30°。这种宽广视场显著优于人类的180°视野，适合在野外环境中捕猎和侦察。双眼重叠区域较大提供了优越的立体视觉，而两侧广阔的单眼区域使其能够敏锐地察觉周围环境的微小变化而无需转头。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。视网膜中含有大量对运动敏感的特殊细胞，能够精确捕捉小型猎物的微小动作。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。这主要得益于其视网膜中含有大量的杆状细胞和高效的反光层(tapetum lucidum)，能够在极低光线下有效收集并增强视觉信号，使其成为优秀的夜间猎手。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。';

          otherFeatures.tearStains = '通常轻微。狸花猫泪道系统发育一般较为良好，不易出现严重泪痕。若出现明显泪痕，需关注是否有潜在的眼部刺激或健康问题。';
          otherFeatures.commonEyeDiseases = '结膜炎、角膜炎、上呼吸道感染并发症是狸花猫相对常见的眼部问题。由于其探索天性，也需注意预防眼部外伤和异物刺激。整体而言，遗传性眼病风险较低。';
          break;
          
     case '黄狸猫':
          basicFeatures.eyeColor = '黄色、金色、绿色等';
          basicFeatures.eyeShape = '圆杏核状。瞳孔调节能力出色，亮光下呈垂直线状，黑暗中完全打开呈圆形，以适应野外多变的光线。';
          basicFeatures.thirdEyelid = '黄狸猫的第三眼睑功能完善，呈半透明白色或略带粉色。瞬膜在其活跃的户外生活中起到关键的保护作用，如清洁眼表、清除小碎片。它的瞬膜响应灵敏，能在需要时快速覆盖角膜，通常不易察觉，除非有健康问题。';
          basicFeatures.eyeExtension = '黄狸猫的眼睛在其金黄色被毛的映衬下显得格外锐利，形成经典的"猎手之眼"。其眼部周围的虎纹状花纹形成了自然的"眼线"装饰，不仅增强了面部表情，还在进化上有助于减少阳光反射，提高低光环境中的视觉敏锐度。其瞳孔调节能力尤为出色，能在瞬间适应从阳光明媚的草地到阴暗角落的光线变化。';

          coreFeatures.visualAngle = '黄狸猫拥有约200°的宽广水平视野，双眼重叠区域约140°，两侧各约30°。这种广阔的视场范围远超人类的180°，帮助它在半野化环境中保持高度警觉性。其大范围的双眼重叠区域提供了精确的深度感知能力，而宽广的周边视觉使它能够同时关注多个方向的潜在威胁或猎物。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。视网膜中含有大量对运动敏感的特殊细胞，能够精确捕捉小型猎物的微小动作。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。黄狸猫具有非常发达的夜视能力，大量的视网膜杆细胞和高效的光反射层使其在几乎完全黑暗的环境中，仍能进行有效的狩猎活动。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。';

          otherFeatures.tearStains = '通常轻微。与狸花猫类似，黄狸猫的泪管系统通常发育完善，泪液排出通畅，很少出现明显泪痕。定期观察即可。';
          otherFeatures.commonEyeDiseases = '结膜炎、角膜炎是常见问题，尤其在环境卫生不佳或与其他猫咪接触密切时。上呼吸道感染也可能引发眼部症状。遗传性眼病相对少见，但个体差异存在。';
          break;

      case '玄猫':
          basicFeatures.eyeColor = '浅黄色、浅绿色、蓝色等';
          basicFeatures.eyeShape = '通常为杏仁状，部分可能更偏向圆形。瞳孔在强光下收缩为极细的垂直线，暗光下充分扩张，配合黑色被毛，夜视能力极佳。';
          basicFeatures.thirdEyelid = '玄猫的第三眼睑通常是半透明白色或略带色素沉着，与黑色被毛形成对比。其功能与其他猫种相同，用于保护、清洁和润滑眼球。由于玄猫整体外观深邃，健康的瞬膜通常隐藏得很好，不易被注意到。';
          basicFeatures.eyeExtension = '玄猫的眼睛在黑色被毛映衬下显得格外明亮，这种对比使其眼睛成为面部表情最突出的特征。黑色被毛减少了眼部周围的光线反射，使瞳孔能更有效地调节，增强了低光环境下的视觉性能。眼睛颜色随着年龄增长可能从幼猫时期的蓝色逐渐变为成年后的黄色或绿色。';

          coreFeatures.visualAngle = '玄猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种宽广的视场配合黑色被毛减少了眼部周围的光线反射，大幅提高了其在低光环境中的视觉性能。双眼重叠区域的扩大使其猎捕能力更为精准，而广阔的周边视野则确保了较高的环境警觉性。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。其黑色被毛提供了额外的视觉优势，减少了眼部周围的杂光干扰，使其在复杂光线环境中也能保持清晰视觉。其运动感知能力特别发达。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。玄猫的纯黑被毛有助于吸收散射光，减少干扰，结合高度发达的视网膜反光层，使其成为夜间视觉性能最佳的猫种之一。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。';

          otherFeatures.tearStains = '轻微至无，不易观察。黑色被毛能很好地掩盖泪痕。生理上泪道系统通常发育良好，泪液溢出问题较少见。';
          otherFeatures.commonEyeDiseases = '结膜炎、角膜炎仍是基础常见病。部分研究提示某些黑色猫咪群体可能有较高的虹膜黑色素瘤风险，但总体罕见。需要关注眼部颜色、形状的异常变化。';
          break;
          
        case '三花猫':
          basicFeatures.eyeColor = '蓝色、绿色、黄色、棕色等';
          basicFeatures.eyeShape = '多为杏仁状或圆形。瞳孔具有典型的猫科调节机制，亮光下呈缝状，暗光下扩张为圆形，光线适应范围广。';
          basicFeatures.thirdEyelid = '三花猫的第三眼睑颜色可能因个体色素分布而略有不同，通常是半透明白色或粉白色，有时边缘可能带有少量色素。瞬膜在清洁眼球表面、移除异物和提供保护方面发挥着关键作用。健康状态下通常不可见。';
          basicFeatures.eyeExtension = '三花猫的眼睛通常表现出明亮且警觉的特质，其独特的遗传组合可能导致眼睛颜色的丰富变化。其眼周毛色通常形成独特图案，这些模式不仅美观，还在进化上有助于掩饰眼睛轮廓，减少被猎物察觉的可能性。三花猫的表情丰富，眼睛能精确传达各种情绪状态。';

          coreFeatures.visualAngle = '三花猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种超宽视场明显优于人类的180°限制，使其在不移动头部的情况下能够监控更大范围的环境。140°的双眼重叠视场提供了精确的立体视觉和深度判断，而两侧各30°的单眼区域则增强了周边威胁的探测能力。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。其特殊的基因组合可能带来更灵活的视觉适应能力。三花猫往往展现出出色的狩猎技巧，这与其精准的视觉跟踪能力密切相关。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-7倍灵敏度。三花猫具有典型的猫科优秀夜视能力，在微弱光线下仍能清晰感知环境。其对光线强度变化的适应速度特别快，能在不同光照条件间迅速切换视觉模式。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。'

          otherFeatures.tearStains = '轻微至中度。泪痕在白色或浅色毛发区域会比较明显。大部分三花猫泪道功能正常，若泪痕严重需检查原因，日常注意清洁浅色毛发区域。';
          otherFeatures.commonEyeDiseases = '结膜炎、角膜炎是普遍问题。由于三花猫几乎都是雌性，与性别相关的眼病（如果存在）理论上风险更高，但实际报道不多。异色瞳个体需关注两眼健康状况。';
          break;
          
        case '奶牛猫':
          basicFeatures.eyeColor = '蓝色、绿色、黄色等';
          basicFeatures.eyeShape = '杏仁状或圆形。瞳孔收缩时呈清晰的垂直狭，扩张时则变为大而圆，能快速响应光线变化。';
          basicFeatures.thirdEyelid = '奶牛猫的第三眼睑（瞬膜）功能健全，通常为半透明白色。它在保持角膜湿润、清除异物以及在睡眠或麻醉时保护眼睛方面起着重要作用。英短的面部结构一般不会导致瞬膜异常突出，健康状态下不明显。';
          basicFeatures.eyeExtension = '奶牛猫的眼睛在黑白分明的脸部形成强烈视觉对比，增强了眼部表情的传达能力。其眼睛周围的黑色毛发类似自然"眼线"，可减少光线反射并增强视觉对比度。这种眼部结构有助于在复杂光线环境中维持清晰视觉，尤其适合在农场等多变环境中捕捉小型猎物。';

          coreFeatures.visualAngle = '奶牛猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种视场布局使其不仅拥有优于人类的环境感知范围，还保持了猎食者所需的精确深度判断能力。圆形的眼睛和面部结构为其提供了良好的前方视觉感知能力，140°的双眼重叠区域则确保了精确的距离评估，适合室内环境下的精细运动控制。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。其视网膜结构均衡，既有不错的细节感知能力，又保留了对运动的敏感度，适合室内生活环境。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。奶牛猫的夜视能力对于室内环境绰绰有余。其眼睛能够在弱光条件下有效收集光线，保持基本活动和探索能力。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。';

          otherFeatures.tearStains = '通常轻微。奶牛猫面部结构通常使得泪液排出顺畅。蓝色或其他浅色英短的泪痕可能相对明显，注意观察和清洁即可。';
          otherFeatures.commonEyeDiseases = '遗传性视网膜疾病（如进行性视网膜萎缩 PRA）、白内障、青光眼是英短需要关注的遗传风险。此外，肥厚性心肌病(HCM)严重时也可能影响眼底血管。定期体检和眼科检查很重要。';

          break;
          
        case '英国短毛':
          basicFeatures.eyeColor = '蓝色、绿色、黄色、棕色等，根据被毛颜色而变';
          basicFeatures.eyeShape = '大而圆。瞳孔在亮光下收缩成垂直缝隙，暗光下扩张时几乎占满整个虹膜，显得眼睛更大更圆，聚光能力强。';
          basicFeatures.thirdEyelid = '英国短毛猫的第三眼睑（瞬膜）功能健全，通常为半透明白色。它在保持角膜湿润、清除异物以及在睡眠或麻醉时保护眼睛方面起着重要作用。英短的面部结构一般不会导致瞬膜异常突出，健康状态下不明显。';
          basicFeatures.eyeExtension ='英国短毛猫的眼睛圆而大，配合其圆润的脸型，形成特有的"泰迪熊"外观。眼睛通常呈铜色或与毛色协调的色调，运动跟踪能力优秀。作为室内伴侣猫，其眼部结构优化了中距离观察，尤其善于监控家庭环境中的动态变化。蓝色英短的蓝眼睛来源于特定基因组合，与其绒蓝色被毛形成完美搭配。';

          coreFeatures.visualAngle = '英国短毛猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种视场布局使其不仅拥有优于人类的环境感知范围，还保持了猎食者所需的精确深度判断能力。圆形的眼睛和面部结构为其提供了良好的前方视觉感知能力，140°的双眼重叠区域则确保了精确的距离评估，适合室内环境下的精细运动控制。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。其视网膜结构均衡，既有不错的细节感知能力，又保留了对运动的敏感度，适合室内生活环境。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。英国短毛猫的夜视能力对于室内环境绰绰有余。其眼睛能够在弱光条件下有效收集光线，保持基本活动和探索能力。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。';

          otherFeatures.tearStains = '通常轻微。英国短毛猫面部结构通常使得泪液排出顺畅。蓝色或其他浅色英短的泪痕可能相对明显，注意观察和清洁即可。';
          otherFeatures.commonEyeDiseases = '遗传性视网膜疾病（如进行性视网膜萎缩 PRA）、白内障、青光眼是英短需要关注的遗传风险。此外，肥厚性心肌病(HCM)严重时也可能影响眼底血管。定期体检和眼科检查很重要。';

          break;
          
        case '美国短毛':
          basicFeatures.eyeColor = '蓝色、绿色、黄色等';
          basicFeatures.eyeShape = '大而圆，稍带杏仁状。瞳孔调节灵敏，亮光下呈缝状保护眼睛，暗光下扩张以捕捉更多光线，适应性强。';
          basicFeatures.thirdEyelid = '美国短毛猫的第三眼睑功能完善，颜色通常为半透明白色或浅粉色。它像一个内置的"雨刮器"，帮助清洁角膜表面，并分泌具有抗菌作用的液体。美短通常瞬膜隐藏良好，除非生病或受到刺激。';
          basicFeatures.eyeExtension = '美国短毛猫的眼睛体现了实用主义特点，大小适中且定位精确，为捕猎提供最佳视野。其眼睛可呈现多种颜色，从金黄到翠绿不等，通常与毛色形成和谐搭配。作为优秀的工作猫，美国短毛猫的瞳孔调节灵活迅速，能在仓库、谷仓等环境中迅速适应光线变化，维持稳定的视觉表现。';

          coreFeatures.visualAngle = '美国短毛猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种视场配置优于人类约20°，其中140°的双眼区域提供了卓越的立体视觉和深度感知能力，特别适合精确判断距离和高度。作为优秀的猎手，两侧各30°的单眼区域增强了对周边运动的警觉性，这对捕捉活动中的猎物至关重要。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。其视网膜结构保留了许多野生祖先的特性，对运动物体的追踪能力特别出色，同时具备不错的静态细节辨识能力。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。'

          otherFeatures.tearStains = '通常轻微。美国短毛猫的泪道系统发育一般良好，泪痕问题较少。银虎斑等浅色被毛个体若有泪痕会相对明显。';
          otherFeatures.commonEyeDiseases = '结膜炎、角膜炎是常见感染性问题。遗传方面，需要关注肥厚性心肌病(HCM)可能带来的眼部并发症。相对而言，美短是眼部健康较为稳定的品种之一。';

          break;
          
        case '布偶':
          basicFeatures.eyeColor = '蓝色';
          basicFeatures.eyeShape = '大而圆。明亮环境下瞳孔收缩为垂直缝隙，但在暗光下能扩张得非常圆，充分利用其大眼睛的优势收集光线。';
          basicFeatures.thirdEyelid = '布偶猫的第三眼睑（瞬膜）呈半透明白色或浅粉色。它在保护大而圆的眼睛免受灰尘、毛发等刺激方面扮演重要角色。由于布偶猫较为放松的性格，有时在打盹或深度放松时瞬膜会部分可见，但这通常是正常的生理现象。';
          basicFeatures.eyeExtension = '布偶猫的蓝眼睛是其最显著的特征之一，源于特定的基因表达。大而圆的眼睛形状使其拥有较大的视野范围，适合室内环境观察。布偶猫眼睛中的瞳孔能在黑暗中极大扩张，这种特性使其能有效利用室内微弱光线，即使在夜间也能自如活动。其深邃的蓝色虹膜中含有丰富的血管和神经末梢，使表情尤为生动。';

          coreFeatures.visualAngle = '布偶猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种视场结构显著宽于人类的180°视野，其大而圆的眼睛提供了良好的前方视觉，尤其适合室内环境的观察和互动。140°的双眼重叠区域确保了精确的深度感知，有助于其在家庭环境中的灵活移动，而两侧的单眼区域则补充了环境感知能力。';
          coreFeatures.visualAcuity = '喵喵的视力辨析度在20/100到20/200之间，理想条件下可达20/30。作为室内伴侣猫，其视觉系统更偏向于近距离观察和社交互动，对人类面部表情的辨识能力特别强。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。布偶猫的夜视能力适应了室内生活环境，虽不及某些户外猎手型猫种，但足以在家庭环境中自如活动。蓝色眼睛可能导致光线散射增加，在极低光环境下视觉清晰度略有降低。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理。';

          otherFeatures.tearStains = '中度常见。布偶猫由于面部结构及浅色重点色被毛，泪痕相对容易出现且较为明显。需要定期仔细清洁眼部周围，防止泪痕氧化变色和皮肤刺激。';
          otherFeatures.commonEyeDiseases = '肥厚性心肌病(HCM)和多囊肾病(PKD)是布偶猫常见的遗传病，严重时可能影响眼部（如高血压性视网膜病变）。结膜炎、角膜炎也时有发生。蓝眼对强光敏感，注意避免刺激。';
  
          break;
          
        case '暹罗':
          basicFeatures.eyeColor = '蓝色';
          basicFeatures.eyeShape = '杏仁状。瞳孔在亮光下收缩成非常细的垂直线，与杏仁眼形相得益彰；暗光下则扩张为圆形，适应其活跃的夜间活动习性。';
          basicFeatures.thirdEyelid = '暹罗猫的第三眼睑是半透明白色或浅粉色。它不仅提供物理保护，还能分泌泪液成分，帮助维持眼表健康。由于暹罗猫眼型和面部结构，有时瞬膜可能比其他品种更容易观察到，尤其是在它们放松或眨眼时。';
          basicFeatures.eyeExtension = '14世纪暹罗皇室培育的庙宇守护猫（历史溯源）,暹罗猫的蓝眼睛源于特殊的温度敏感基因，这种基因导致面部、耳朵等较冷区域的色素沉淀。其眼睛的杏仁形和略微倾斜的特点来自东南亚祖先，有助于扩大视野并加强深度感知。暹罗猫的瞳孔对光线变化反应灵敏，能迅速适应不同环境，这一特性使其成为出色的室内猎手。';

          coreFeatures.visualAngle = '暹罗猫拥有约200°的水平视野，双眼重叠区域约140°，两侧各约30°。这种视场构造比人类多出约20°的感知范围，其杏仁形且略微倾斜的眼睛提供了独特的视觉优势。140°的广阔双眼视野带来精确的立体感知和深度评估，尤其在追踪移动物体方面表现卓越，而30°的周边单眼区域则作为环境监控的补充，使暹罗猫拥有极佳的捕猎感知。';
          coreFeatures.visualAcuity = '暹罗猫的视力辨析度优于许多家猫品种，接近人类的二分之一。其视网膜结构保留了许多东南亚野生祖先的特性，对快速移动目标的追踪能力特别出色，静态视力也较为清晰。不过，部分暹罗猫可能存在轻微的视轴对位异常（斗鸡眼）导致视敏度相对下降。';
          coreFeatures.nightVision = '夜视能力约高出人眼6-8倍灵敏度。暹罗猫的夜视能力非常出色，与其热带祖先的夜间狩猎习性密切相关。尽管蓝色眼睛可能导致光线散射略有增加，但其视网膜结构仍保证了卓越的低光视觉表现。';
          coreFeatures.motionPerception = '猫科动物对运动的感知能力远强于人类，能捕捉高达80fps的动态图像（人类约为60fps）。视网膜中特化的神经节细胞专门负责检测运动变化和视觉信息处理,前庭-视觉反射延迟仅22ms（家猫平均35ms），可在1.5米高空坠落中实时调整落地姿态（应用场景）,猫眼能即时发现视野中极微小的动态变化，这使它们成为出色的猎手。';

          otherFeatures.tearStains = '中度常见。暹罗猫的泪痕在其浅色的面部重点色区域尤为明显。部分个体可能因泪道结构问题导致泪液分泌过多或排出不畅。需要定期清洁以防色素沉着。';
          otherFeatures.commonEyeDiseases = '进行性视网膜萎缩(PRA)、斜视（内斜视为品种特征，通常不影响生活）、青光眼、眼球震颤是暹罗猫相对高发的眼部问题。此外，对麻醉药物可能更敏感，眼科手术需谨慎。';

          break;

      default:
        // 使用默认值
        break;
    }
    
    // 更新页面数据
    page.setData({
      'visionTabs.basicFeatures': basicFeatures,
      'visionTabs.coreFeatures': coreFeatures,
      'visionTabs.otherFeatures': otherFeatures,
      // 更新视觉参数
      'dogVisionParams': visionParams,
      // 同时更新原始参数，用于重置
      'originalDogVisionParams.brightness': visionParams.brightness,
      'originalDogVisionParams.contrast': visionParams.contrast,
      'originalDogVisionParams.resolutionFactor': visionParams.resolutionFactor,
      'originalDogVisionParams.antiAliasFactor': visionParams.antiAliasFactor,
      // 更新猫科动物标识
      'features.isCat': isCat
    });
    
    console.log('视觉标签页数据和视觉参数已更新:', {
      basicFeatures,
      coreFeatures,
      otherFeatures,
      visionParams
    });
    
  } catch (error) {
    console.error('更新视觉标签页数据错误:', error);
  }
}

// 重新导出标签页切换功能
function changeTab(index, page) {
  return tabManager.changeTab(index, page);
}

function swiperChange(event, page) {
  return tabManager.swiperChange(event, page);
}

// 重新导出视图切换功能
function toggleView(page) {
  // 先切换视图
  viewManager.toggleView(page);

  // 确保在切换视角时更新视觉标签页数据
  const app = getApp();
  const selectedBreed = app.globalData.selectedBreed;

  if (selectedBreed) {
    // 重新更新视觉标签页数据，确保与当前选择的品种保持同步
    updateVisionTabsData(selectedBreed, page);
  }
}

function switchCameraPosition(page) {
  return viewManager.switchCameraPosition(page);
          console.log('相机切换超时，尝试再次初始化');
          page.initCamera();
        }
      }, 3000); // 缩短检查时间为3秒
      
    }, 300); // 缩短延迟时间，减少用户等待时间
  } catch (error) {
    console.error('切换相机位置错误:', error);
    // 出错时重置相机
    page.setData({ 
      cameraLoading: false,
      isLowLight: false
    });
    page.initCamera();
  }
}

// 重新导出视觉参数管理功能
function toggleVisionParams(page) {
  return visionParamsManager.toggleVisionParams(page);
}

function resetVisionParams(page) {
  return visionParamsManager.resetVisionParams(page);
  try {
    const originalParams = page.data.originalDogVisionParams;
    const selectedBreed = page.data.breedName;
    const isCat = page.data.features.isCat;
    
    // 导入视觉配置
    const visionConfig = require('./vision-config');
    
    // 创建新的参数对象
    const resetParams = {
      resolutionFactor: originalParams.resolutionFactor,
      antiAliasFactor: originalParams.antiAliasFactor,
      brightness: originalParams.brightness,
      contrast: originalParams.contrast,
      motionSensitivity: isCat ? visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SENSITIVITY : visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY, // 使用配置文件中对应动物类型的值
      motionThreshold: isCat ? visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_THRESHOLD : visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD, // 使用配置文件中的值
      motionSizeThreshold: isCat ? visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SIZE_THRESHOLD : visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD // 使用配置文件中的值
    };
    
    // 根据品种调整特定参数
    if (!isCat) { // 犬科动物特定调整
      switch (selectedBreed) {
        case '哈士奇':
          resetParams.brightness = 1.8; // 哈士奇夜视能力更强
          resetParams.resolutionFactor = 0.27; // 哈士奇视力约为人类的4/15
          break;
          
        case '边牧':
        case '边境牧羊':
          resetParams.resolutionFactor = 0.4; // 边牧视力约为人类的2/5
          resetParams.motionSensitivity = visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY; // 使用配置文件中的值
          break;
          
        case '金毛':
        case '金毛寻回':
          resetParams.resolutionFactor = 0.33; // 金毛视力约为人类的1/3
          break;
          
        case '柯基':
          resetParams.resolutionFactor = 0.27; // 柯基视力约为人类的4/15
          break;
          
        case '泰迪':
        case '贵宾(泰迪)':
          resetParams.resolutionFactor = 0.27; // 泰迪视力约为人类的4/15
          resetParams.brightness = 1.3; // 泰迪夜视能力较弱
          break;
          
        case '拉布拉多':
          resetParams.resolutionFactor = 1.0; // 拉布拉多视力接近于人类
          resetParams.brightness = 1.6; // 拉布拉多夜视能力较强
          break;
          
        case '吉娃娃':
        case '法国斗牛':
          resetParams.resolutionFactor = 0.25; // 约为人类的1/4
          break;
          
        default:
          // 使用默认值
          break;
      }
    } else { // 猫科动物特定调整
      switch (selectedBreed) {
        case '暹罗':
          // 设置暹罗猫的特殊参数
          resetParams.resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.SIAMESE_RESOLUTION_FACTOR; // 0.15
          resetParams.brightness = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_BRIGHTNESS; // 1.5
          resetParams.contrast = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_CONTRAST; // 1.3
          break;
        case '英国短毛':
        case '美国短毛':
        case '布偶':
        default:
          // 其他猫科动物使用默认参数
          break;
      }
    }
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.resolutionFactor': resetParams.resolutionFactor,
      'dogVisionParams.antiAliasFactor': resetParams.antiAliasFactor,
      'dogVisionParams.brightness': resetParams.brightness,
      'dogVisionParams.contrast': resetParams.contrast,
      'dogVisionParams.motionSensitivity': resetParams.motionSensitivity,
      'dogVisionParams.motionThreshold': resetParams.motionThreshold,
      'dogVisionParams.motionSizeThreshold': resetParams.motionSizeThreshold
    }, () => {
      // 数据更新后立即触发一次帧处理，确保视觉效果实时更新
      if (page.processFrameWebGL && page._lastFrame) {
        page.processFrameWebGL(page._lastFrame);
      }
      
      wx.showToast({
        title: '参数已重置',
        icon: 'success',
        duration: 1500
      });
    });
    
  } catch (error) {
    console.error('重置视觉参数错误:', error);
    wx.showToast({
      title: '重置失败',
      icon: 'none',
      duration: 1500
    });
  }
}

function onBrightnessChange(event, page) {
  return visionParamsManager.onBrightnessChange(event, page);
}

function onContrastChange(event, page) {
  return visionParamsManager.onContrastChange(event, page);
  });
}

function onResolutionChange(event, page) {
  return visionParamsManager.onResolutionChange(event, page);
}

function onAntiAliasChange(event, page) {
  return visionParamsManager.onAntiAliasChange(event, page);
}

module.exports = {
  updateVisionTabsData,
  changeTab,
  swiperChange,
  toggleView,
  switchCameraPosition,
  toggleVisionParams,
  resetVisionParams,
  onBrightnessChange,
  onContrastChange,
  onResolutionChange,
  onAntiAliasChange
};
