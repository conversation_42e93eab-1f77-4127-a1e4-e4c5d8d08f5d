/**
 * 分析区域相关样式模块
 * 包含分析容器、标签页、内容展示等样式
 */

/* 分析容器基础样式 */
.analysis-container {
  flex: 1;
  background: rgba(40, 60, 90, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  min-height: 30vh;
  max-height: 70vh;
  overflow: hidden;
}

/* 展开状态 */
.analysis-container.expanded {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 150;
  min-height: 100vh;
  max-height: 100vh;
  border-top: none;
}

/* 浅色主题下的分析容器样式 */
.light-theme-content .analysis-container {
  background: rgba(250, 250, 250, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

/* 分析头部 */
.analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(50, 70, 110, 0.8);
  backdrop-filter: blur(15px);
}

.light-theme-content .analysis-header {
  background: rgba(240, 240, 240, 0.9);
  border-bottom-color: rgba(0, 0, 0, 0.08);
}

.analysis-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.5rpx;
}

.light-theme-content .analysis-title {
  color: rgba(0, 0, 0, 0.9);
}

.expand-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme-content .expand-btn {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.7);
  border-color: rgba(0, 0, 0, 0.1);
}

.expand-btn:active {
  background: rgba(255, 138, 91, 0.2);
  border-color: rgba(255, 138, 91, 0.4);
  transform: scale(0.9);
}

.expand-btn.expanded {
  transform: rotate(180deg);
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: rgba(60, 80, 120, 0.6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding: 0 20rpx;
}

.light-theme-content .tab-nav {
  background: rgba(230, 230, 230, 0.8);
  border-bottom-color: rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  position: relative;
  border-radius: 12rpx 12rpx 0 0;
  margin: 0 5rpx;
}

.light-theme-content .tab-item {
  color: rgba(0, 0, 0, 0.6);
}

.tab-item.active {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 138, 91, 0.15);
  border: 1px solid rgba(255, 138, 91, 0.2);
  border-bottom: none;
}

.light-theme-content .tab-item.active {
  color: rgba(0, 0, 0, 0.9);
  background: rgba(255, 138, 91, 0.1);
}

.tab-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  right: 20%;
  height: 2rpx;
  background: rgba(255, 138, 91, 0.8);
  opacity: 0;
  transition: all 0.3s ease;
}

.tab-item.active::after {
  opacity: 1;
}

/* 标签页内容 */
.tab-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tab-scroll-view {
  height: 100%;
  padding: 30rpx 40rpx;
  padding-bottom: 50rpx;
}

/* 展开时的内容区域样式 */
.analysis-container.expanded .tab-scroll-view {
  height: calc(100% - 100rpx);
  padding-bottom: 50rpx;
}

/* 内容项样式 */
.content-item {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
}

.light-theme-content .content-item {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.08);
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-item:active {
  background: rgba(255, 255, 255, 0.08);
  transform: scale(0.98);
}

.light-theme-content .content-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.content-label {
  font-size: 26rpx;
  font-weight: 600;
  color: rgba(255, 138, 91, 0.9);
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.content-value {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.light-theme-content .content-value {
  color: rgba(0, 0, 0, 0.9);
}

.content-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-top: 16rpx;
}

.light-theme-content .content-desc {
  color: rgba(0, 0, 0, 0.7);
}

/* 特殊内容样式 */
.highlight-value {
  color: rgba(255, 138, 91, 0.9);
  font-weight: 600;
}

.warning-value {
  color: rgba(255, 193, 7, 0.9);
  font-weight: 600;
}

.success-value {
  color: rgba(40, 167, 69, 0.9);
  font-weight: 600;
}

/* 数据展示网格 */
.data-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.data-item {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12rpx;
  border: 1px solid rgba(255, 255, 255, 0.05);
  text-align: center;
}

.light-theme-content .data-item {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.05);
}

.data-item-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.light-theme-content .data-item-label {
  color: rgba(0, 0, 0, 0.6);
}

.data-item-value {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 138, 91, 0.9);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .analysis-header {
    padding: 24rpx 30rpx 16rpx;
  }
  
  .analysis-title {
    font-size: 28rpx;
  }
  
  .expand-btn {
    width: 50rpx;
    height: 50rpx;
    font-size: 28rpx;
  }
  
  .tab-item {
    height: 70rpx;
    font-size: 24rpx;
  }
  
  .tab-scroll-view {
    padding: 24rpx 30rpx;
  }
  
  .content-item {
    padding: 24rpx;
    margin-bottom: 30rpx;
  }
  
  .content-label {
    font-size: 24rpx;
  }
  
  .content-value {
    font-size: 26rpx;
  }
  
  .content-desc {
    font-size: 22rpx;
  }
  
  .data-grid {
    gap: 16rpx;
  }
  
  .data-item {
    padding: 16rpx;
  }
  
  .data-item-label {
    font-size: 20rpx;
  }
  
  .data-item-value {
    font-size: 28rpx;
  }
}
