/**
 * 生命周期管理器
 * 负责管理页面的生命周期方法，包括onLoad、onShow、onHide、onUnload等
 * 从主文件中提取生命周期相关的逻辑，提高代码可维护性
 */

// 导入相关模块
const pageDataManager = require('./page-data-manager');
const uiController = require('../utils/ui-controller');
const utils = require('../utils/utils');

/**
 * 页面加载时的初始化逻辑
 * @param {Object} page - 页面实例
 * @param {Object} options - 页面参数
 */
function onLoad(page, options) {
  console.log('视觉模拟页面加载开始');
  
  // 设置页面实例的全局引用
  global.visionSimulationPage = page;
  
  // 初始化页面数据
  const initialData = pageDataManager.getInitialPageData();
  const dogVisionParams = pageDataManager.getDogVisionParams();
  const originalDogVisionParams = pageDataManager.getOriginalDogVisionParams();
  const tabsData = pageDataManager.getTabsData();
  const visionModes = pageDataManager.getVisionModes();
  
  // 合并所有初始数据
  const allInitialData = {
    ...initialData,
    dogVisionParams,
    originalDogVisionParams,
    ...tabsData,
    visionModes
  };
  
  // 设置初始数据
  page.setData(allInitialData);
  
  // 获取全局应用实例
  const app = getApp();
  
  // 获取品种信息
  const selectedBreed = app.globalData.selectedBreed;
  const breedType = app.globalData.breedType;
  
  if (selectedBreed) {
    console.log('获取到选中的品种:', selectedBreed);
    
    // 更新品种相关数据
    page.setData({
      breedName: selectedBreed.name || '',
      breedDetails: selectedBreed,
      'features.isCat': breedType === 'cat'
    });
    
    // 更新视觉标签页数据
    uiController.updateVisionTabsData(selectedBreed, page);
  } else {
    console.warn('未获取到品种信息，使用默认设置');
  }
  
  // 检测设备性能
  const devicePerformance = utils.detectDevicePerformance ? utils.detectDevicePerformance() : 'medium';
  page.setData({ devicePerformance });
  
  // 初始化相机
  if (typeof page.initCamera === 'function') {
    page.initCamera();
  }
  
  // 初始化WebGL
  if (typeof page.initWebGL === 'function') {
    page.initWebGL();
  }
  
  // 启动FPS监控
  if (typeof page.startFPSMonitoring === 'function') {
    page.startFPSMonitoring();
  }
  
  console.log('视觉模拟页面加载完成');
}

/**
 * 页面显示时的逻辑
 * @param {Object} page - 页面实例
 */
function onShow(page) {
  console.log('视觉模拟页面显示');
  
  // 重新获取品种信息（可能在其他页面有更新）
  const app = getApp();
  const selectedBreed = app.globalData.selectedBreed;
  
  if (selectedBreed && selectedBreed.name !== page.data.breedName) {
    console.log('品种信息已更新，重新设置:', selectedBreed);
    
    page.setData({
      breedName: selectedBreed.name || '',
      breedDetails: selectedBreed,
      'features.isCat': app.globalData.breedType === 'cat'
    });
    
    // 更新视觉标签页数据
    uiController.updateVisionTabsData(selectedBreed, page);
  }
  
  // 恢复相机（如果需要）
  if (page.data.cameraError && typeof page.initCamera === 'function') {
    console.log('尝试恢复相机');
    page.initCamera();
  }
  
  // 恢复FPS监控
  if (typeof page.startFPSMonitoring === 'function') {
    page.startFPSMonitoring();
  }
  
  // 重新启动帧处理（如果WebGL已初始化）
  if (page._webglInitialized && typeof page.startFrameProcessing === 'function') {
    page.startFrameProcessing();
  }
}

/**
 * 页面隐藏时的逻辑
 * @param {Object} page - 页面实例
 */
function onHide(page) {
  console.log('视觉模拟页面隐藏');
  
  // 停止FPS监控
  if (typeof page.stopFPSMonitoring === 'function') {
    page.stopFPSMonitoring();
  }
  
  // 暂停帧处理以节省资源
  if (typeof page.pauseFrameProcessing === 'function') {
    page.pauseFrameProcessing();
  }
  
  // 清理定时器
  if (page._fpsUpdateInterval) {
    clearInterval(page._fpsUpdateInterval);
    page._fpsUpdateInterval = null;
  }
}

/**
 * 页面卸载时的清理逻辑
 * @param {Object} page - 页面实例
 */
function onUnload(page) {
  console.log('视觉模拟页面卸载开始');
  
  // 停止所有定时器
  if (page._fpsUpdateInterval) {
    clearInterval(page._fpsUpdateInterval);
    page._fpsUpdateInterval = null;
  }
  
  if (page._memoryMonitorTimer) {
    clearInterval(page._memoryMonitorTimer);
    page._memoryMonitorTimer = null;
  }
  
  // 释放WebGL资源
  if (typeof page.releaseWebGLResources === 'function') {
    page.releaseWebGLResources();
  }
  
  // 停止相机
  if (typeof page.stopCamera === 'function') {
    page.stopCamera();
  }
  
  // 清理全局引用
  if (global.visionSimulationPage === page) {
    global.visionSimulationPage = null;
  }
  
  // 清理页面数据
  page.setData({
    cameraLoading: true,
    cameraError: false,
    currentFPS: 0,
    averageBrightness: 0,
    isLowLight: false,
    isBrightLight: false
  });
  
  console.log('视觉模拟页面卸载完成');
}

/**
 * 页面下拉刷新
 * @param {Object} page - 页面实例
 */
function onPullDownRefresh(page) {
  console.log('页面下拉刷新');
  
  // 重新初始化相机
  if (typeof page.initCamera === 'function') {
    page.initCamera();
  }
  
  // 重置错误状态
  page.setData({
    cameraError: false,
    cameraErrorMsg: ''
  });
  
  // 停止下拉刷新动画
  setTimeout(() => {
    wx.stopPullDownRefresh();
  }, 1000);
}

/**
 * 页面触底加载
 * @param {Object} page - 页面实例
 */
function onReachBottom(page) {
  // 视觉模拟页面通常不需要触底加载
  console.log('页面触底');
}

/**
 * 页面分享
 * @param {Object} page - 页面实例
 * @param {Object} options - 分享参数
 * @returns {Object} 分享配置
 */
function onShareAppMessage(page, options) {
  const breedName = page.data.breedName || '爱宠';
  
  return {
    title: `体验${breedName}的视觉世界`,
    path: '/pages/index/index',
    imageUrl: '/images/share-image.png'
  };
}

/**
 * 页面分享到朋友圈
 * @param {Object} page - 页面实例
 * @returns {Object} 分享配置
 */
function onShareTimeline(page) {
  const breedName = page.data.breedName || '爱宠';
  
  return {
    title: `${breedName}眼中的世界是这样的！`,
    imageUrl: '/images/share-image.png'
  };
}

module.exports = {
  onLoad,
  onShow,
  onHide,
  onUnload,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline
};
