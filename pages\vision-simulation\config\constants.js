/**
 * 常量定义文件
 * 统一管理应用中使用的各种常量
 */

/**
 * 视觉处理常量
 */
const VISION_CONSTANTS = {
  // 视觉模式ID
  MODES: {
    DICHROMATIC: 'dichromatic',
    ACUITY: 'acuity',
    NIGHT_VISION: 'nightVision',
    MOTION_VISION: 'motionVision'
  },
  
  // 动物类型
  ANIMAL_TYPES: {
    DOG: 'dog',
    CAT: 'cat'
  },
  
  // 视角类型
  VIEW_TYPES: {
    HUMAN: 'human',
    PET: 'dog'
  }
};

/**
 * 相机常量
 */
const CAMERA_CONSTANTS = {
  // 相机位置
  POSITIONS: {
    BACK: 'back',
    FRONT: 'front'
  },
  
  // 相机类型
  TYPES: {
    BACK: 'back',
    FRONT: 'front',
    CAM: 'cam'
  },
  
  // 分辨率等级
  RESOLUTION_LEVELS: {
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low'
  },
  
  // 帧尺寸
  FRAME_SIZES: {
    LARGE: 'large',
    MEDIUM: 'medium',
    SMALL: 'small'
  }
};

/**
 * UI常量
 */
const UI_CONSTANTS = {
  // 标签页索引
  TAB_INDICES: {
    VISUAL_FEATURES: 0,
    PERCEPTION_ABILITIES: 1,
    PET_CARE: 2
  },
  
  // 主题名称
  THEME_NAMES: {
    THEME1: 'theme1',
    THEME2: 'theme2',
    THEME3: 'theme3'
  },
  
  // 控制面板状态
  PANEL_STATES: {
    SHOW: true,
    HIDE: false
  },
  
  // 展开状态
  EXPAND_STATES: {
    EXPANDED: true,
    COLLAPSED: false
  }
};

/**
 * 状态常量
 */
const STATE_CONSTANTS = {
  // 加载状态
  LOADING_STATES: {
    LOADING: true,
    LOADED: false
  },
  
  // 错误状态
  ERROR_STATES: {
    ERROR: true,
    NO_ERROR: false
  },
  
  // CAM连接状态
  CAM_STATES: {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    ERROR: 'error'
  },
  
  // 设备性能等级
  PERFORMANCE_LEVELS: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high'
  }
};

/**
 * 事件常量
 */
const EVENT_CONSTANTS = {
  // 页面生命周期事件
  LIFECYCLE_EVENTS: {
    ON_LOAD: 'onLoad',
    ON_SHOW: 'onShow',
    ON_HIDE: 'onHide',
    ON_UNLOAD: 'onUnload'
  },
  
  // 用户交互事件
  USER_EVENTS: {
    TAB_CHANGE: 'changeTab',
    SWIPER_CHANGE: 'swiperChange',
    VIEW_TOGGLE: 'toggleView',
    CAMERA_SWITCH: 'switchCamera'
  },
  
  // 系统事件
  SYSTEM_EVENTS: {
    CAMERA_FRAME: 'cameraFrame',
    WEBGL_INIT: 'webglInit',
    ERROR_OCCURRED: 'errorOccurred'
  }
};

/**
 * 数值常量
 */
const NUMERIC_CONSTANTS = {
  // 时间相关
  TIMEOUTS: {
    SHORT: 1000,
    MEDIUM: 3000,
    LONG: 5000,
    VERY_LONG: 10000
  },
  
  // 动画时长
  ANIMATION_DURATIONS: {
    FAST: 200,
    NORMAL: 300,
    SLOW: 500,
    VERY_SLOW: 800
  },
  
  // 阈值
  THRESHOLDS: {
    LOW_LIGHT: 70,
    HIGH_LIGHT: 210,
    FPS_WARNING: 15,
    FPS_CRITICAL: 10
  },
  
  // 默认值
  DEFAULTS: {
    FPS: 30,
    BRIGHTNESS: 0,
    CONTRAST: 0,
    RESOLUTION_INDEX: 1,
    TAB_INDEX: 0
  }
};

/**
 * 字符串常量
 */
const STRING_CONSTANTS = {
  // 错误消息
  ERROR_MESSAGES: {
    CAMERA_INIT_FAILED: '相机初始化失败',
    WEBGL_INIT_FAILED: 'WebGL初始化失败',
    PERMISSION_DENIED: '权限被拒绝',
    NETWORK_ERROR: '网络连接错误',
    UNKNOWN_ERROR: '未知错误'
  },
  
  // 成功消息
  SUCCESS_MESSAGES: {
    CAMERA_SWITCHED: '相机切换成功',
    VIEW_CHANGED: '视角切换成功',
    PARAMS_RESET: '参数重置成功',
    SETTINGS_SAVED: '设置保存成功'
  },
  
  // 提示消息
  TIP_MESSAGES: {
    LOADING: '加载中...',
    PROCESSING: '处理中...',
    CONNECTING: '连接中...',
    PLEASE_WAIT: '请稍候...'
  },
  
  // 标签文本
  LABELS: {
    HUMAN_VIEW: '人类视角',
    PET_VIEW: '宠物视角',
    BACK_CAMERA: '后置相机',
    FRONT_CAMERA: '前置相机',
    CAM_DEVICE: '硬件相机'
  }
};

/**
 * 正则表达式常量
 */
const REGEX_CONSTANTS = {
  // 数据提取正则
  DATA_EXTRACTION: {
    VISUAL_ANGLE: /水平视野: (\d+)°/,
    NIGHT_VISION: /人类的(\d+(?:\.\d+)?)倍/,
    MOTION_CAPTURE: /运动捕捉率: (\d+)fps/
  },
  
  // 验证正则
  VALIDATION: {
    POSITIVE_NUMBER: /^\d*\.?\d+$/,
    INTEGER: /^\d+$/,
    FLOAT: /^\d*\.\d+$/
  }
};

/**
 * 配置键常量
 */
const CONFIG_KEYS = {
  // 存储键
  STORAGE_KEYS: {
    USER_SETTINGS: 'userSettings',
    VISION_PARAMS: 'visionParams',
    CAMERA_CONFIG: 'cameraConfig',
    THEME_SETTING: 'themeSetting'
  },
  
  // 全局数据键
  GLOBAL_DATA_KEYS: {
    SELECTED_BREED: 'selectedBreed',
    BREED_TYPE: 'breedType',
    CURRENT_THEME: 'currentTheme',
    USER_INFO: 'userInfo'
  }
};

/**
 * API相关常量
 */
const API_CONSTANTS = {
  // 请求方法
  METHODS: {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE'
  },
  
  // 响应状态码
  STATUS_CODES: {
    SUCCESS: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
  },
  
  // 内容类型
  CONTENT_TYPES: {
    JSON: 'application/json',
    FORM: 'application/x-www-form-urlencoded',
    MULTIPART: 'multipart/form-data'
  }
};

/**
 * 获取常量值的辅助函数
 */
const HELPERS = {
  /**
   * 检查值是否为有效的视觉模式
   * @param {string} mode - 模式值
   * @returns {boolean} 是否有效
   */
  isValidVisionMode(mode) {
    return Object.values(VISION_CONSTANTS.MODES).includes(mode);
  },
  
  /**
   * 检查值是否为有效的相机类型
   * @param {string} type - 相机类型
   * @returns {boolean} 是否有效
   */
  isValidCameraType(type) {
    return Object.values(CAMERA_CONSTANTS.TYPES).includes(type);
  },
  
  /**
   * 检查值是否为有效的动物类型
   * @param {string} type - 动物类型
   * @returns {boolean} 是否有效
   */
  isValidAnimalType(type) {
    return Object.values(VISION_CONSTANTS.ANIMAL_TYPES).includes(type);
  },
  
  /**
   * 获取错误消息
   * @param {string} errorType - 错误类型
   * @returns {string} 错误消息
   */
  getErrorMessage(errorType) {
    return STRING_CONSTANTS.ERROR_MESSAGES[errorType] || STRING_CONSTANTS.ERROR_MESSAGES.UNKNOWN_ERROR;
  }
};

module.exports = {
  VISION_CONSTANTS,
  CAMERA_CONSTANTS,
  UI_CONSTANTS,
  STATE_CONSTANTS,
  EVENT_CONSTANTS,
  NUMERIC_CONSTANTS,
  STRING_CONSTANTS,
  REGEX_CONSTANTS,
  CONFIG_KEYS,
  API_CONSTANTS,
  HELPERS
};
