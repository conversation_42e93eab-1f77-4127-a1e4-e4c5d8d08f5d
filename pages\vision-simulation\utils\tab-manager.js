/**
 * 标签页管理模块
 * 负责处理标签页切换和相关UI逻辑
 * 从ui-controller.js中提取标签页相关功能
 */

/**
 * 切换标签页
 * @param {number} index - 标签页索引
 * @param {Object} page - 页面实例
 */
function changeTab(index, page) {
  if (page.data.currentTab === index) return;
  
  page.setData({
    currentTab: index
  });
}

/**
 * 滑动切换标签页
 * @param {Object} event - 事件对象
 * @param {Object} page - 页面实例
 */
function swiperChange(event, page) {
  const index = event.detail.current;
  changeTab(index, page);
}

/**
 * 更新视觉标签页数据
 * @param {Object} breed - 品种信息
 * @param {Object} page - 页面实例
 */
function updateVisionTabsData(breed, page) {
  if (!breed) return;
  
  try {
    // 解析品种详情数据
    const details = breed.details || {};
    const colorPerception = details.colorPerception || '';
    const nightVisionAbility = details.nightVisionAbility || '';
    const visualField = details.visualField || '';
    
    // 判断是否为猫科动物
    const isCat = breed.category === 'cat';
    const catBreed = isCat ? breed.name : '';
    const isHusky = !isCat && breed.name && breed.name.includes('哈士奇');
    
    // 从视野范围中提取数据
    const visualAngleMatch = visualField.match(/水平视野: (\d+)°/);
    const visualAngle = visualAngleMatch ? visualAngleMatch[1] + '°' : '240°';
    
    // 从夜视能力中提取数据
    const nightVisionMatch = nightVisionAbility.match(/人类的(\d+(?:\.\d+)?)倍/);
    const nightVision = nightVisionMatch ? `人类的${nightVisionMatch[1]}倍` : '人类的5倍';
    
    // 从视野范围中提取运动捕捉数据
    const motionMatch = visualField.match(/运动捕捉率: (\d+)fps/);
    
    // 基础变量
    let eyeColor = '琥珀色';
    let eyeShapeDescription = '圆形至椭圆形，带有可变大小的瞳孔，能根据光线条件自动调节';
    let visualFieldDescription = `水平视野约${visualAngle}，远超人类的180°，使汪汪能够更广泛地感知周围环境变化，尤其适合追踪和察觉侧面移动的目标。`;
    let visualAcuityDescription = '汪汪视力辨析度通常为人类的1/4-1/5，这意味着汪汪需要更接近才能看清人类在远处就能分辨的细节。这种特性使汪汪更依赖运动和气味线索而非静态视觉细节。';
    let nightVisionDescription = `人类的5倍，主要得益于视网膜中杆细胞密度高且具有tapetum lucidum（视网膜反光层），能反射微弱光线二次穿过视网膜，显著提升低光环境下的视觉能力。`;
    
    // 根据动物类型设置不同的色彩感知描述
    let colorSystemDescription = isCat ? 
      '喵喵动物视觉系统主要优化用于低光环境，色彩感知比人类弱，但运动敏感度和夜视能力远超人类。猫眼能感知蓝色和绿色光谱，对红色区分度低，在黄昏和夜间环境中有优越表现。' : 
      '汪汪为二色视觉系统，主要感知蓝色和黄色的光谱，对红绿色调的区分能力较弱。这种视觉系统在低光环境下表现更佳，尤其适合黎明和黄昏的狩猎活动。';
    
    let tearStainsDescription = '轻微，仅在情绪激动或有眼部刺激时可能出现，健康状态下泪腺分泌平衡，泪管引流正常。';
    let commonEyeDiseasesDescription = '常见眼部疾病包括结膜炎、角膜炎、白内障、青光眼和进行性视网膜萎缩(PRA)。不同品种有不同的遗传性眼部疾病倾向，定期眼科检查和注意眼部卫生是预防眼疾的关键。';
    
    // 添加第三眼睑描述
    let thirdEyelidDescription = '第三眼睑（又称瞬膜或眩膜）是犬科动物眼睛内的一种半透明保护膜，位于内眼角。它能横向移动覆盖视网膜，帮助清除异物、分泌润滑液并保护眼睛免受伤害。在健康状态下通常不明显，仅在狗狗疲惫、患病或受到刺激时才会部分露出。如果经常可见，可能表明存在健康问题。';
    
    // 创建模拟精度描述
    let simulationAccuracyDescription = '本应用使用WebGL技术模拟爱宠视觉，基于兽医学和动物视觉研究提供的数据。模拟了色彩感知、视场角度、亮度敏感度等核心特性，精度约为90%。实际动物视觉体验可能因个体差异、年龄和健康状况而有所不同。';
    
    // 根据品种特性调整描述
    if (isCat) {
      // 猫科动物特性调整
      eyeColor = details.eyeColor || '绿色或黄色';
      eyeShapeDescription = '椭圆形，瞳孔可从圆形变为垂直缝隙状，适应不同光线条件';
      visualFieldDescription = `水平视野约200°，垂直视野约130°，比人类更广阔的视野范围使猫咪能够敏锐察觉周围的细微变化，特别适合夜间狩猎。`;
      visualAcuityDescription = '猫咪视力辨析度约为人类的1/6-1/10，远距离视觉相对模糊，但近距离（2-6米）视觉清晰，更依赖运动检测和其他感官。';
      nightVisionDescription = `人类的6-8倍，拥有更多的杆细胞和更发达的tapetum lucidum，使猫咪在极低光环境下仍能有效狩猎和导航。`;
      colorSystemDescription = '猫咪为二色视觉系统，主要感知蓝色和绿色光谱，对红色几乎无法区分。这种视觉系统在夜间和低光环境下表现卓越，完美适应夜行性生活方式。';
      tearStainsDescription = '较少见，主要出现在扁脸品种（如波斯猫、加菲猫）中，健康猫咪泪腺分泌适中。';
      commonEyeDiseasesDescription = '常见眼部疾病包括结膜炎、角膜溃疡、青光眼、白内障和视网膜疾病。某些品种（如波斯猫）更容易出现泪管阻塞和眼部感染。';
      thirdEyelidDescription = '第三眼睑在猫咪中更为明显，是重要的眼部保护结构。健康猫咪的第三眼睑通常隐藏在内眼角，只有在睡觉、生病或受到惊吓时才会部分显露。持续可见可能表明眼部疾病或全身性疾病。';
    }
    
    // 哈士奇特殊处理
    if (isHusky) {
      eyeColor = '蓝色或异色';
      nightVisionDescription = '人类的6倍，作为雪橇犬，哈士奇在雪地和低光环境中具有卓越的视觉适应能力，蓝色眼睛在雪地反光环境中表现更佳。';
      visualFieldDescription = `水平视野约${visualAngle}，作为工作犬种，哈士奇具有出色的周边视觉，能够在奔跑中敏锐察觉地形变化和障碍物。`;
    }
    
    // 构建标签页数据
    const visionTabsData = {
      basicFeatures: {
        eyeColor: eyeColor,
        eyeShape: eyeShapeDescription,
        colorSystem: colorSystemDescription,
        visualAngle: visualFieldDescription
      },
      coreFeatures: {
        visualAcuity: visualAcuityDescription,
        nightVision: nightVisionDescription,
        motionPerception: motionMatch ? `${motionMatch[1]}fps运动捕捉率，远超人类25fps的感知能力` : '优于人类2倍的运动感知能力，能够捕捉细微的移动变化'
      },
      otherFeatures: {
        tearStains: tearStainsDescription,
        commonEyeDiseases: commonEyeDiseasesDescription,
        thirdEyelid: thirdEyelidDescription,
        simulationAccuracy: simulationAccuracyDescription
      }
    };
    
    // 更新页面数据
    page.setData({
      visionTabs: visionTabsData
    });
    
    console.log('视觉标签页数据更新完成:', visionTabsData);
    
  } catch (error) {
    console.error('更新视觉标签页数据时出错:', error);
  }
}

module.exports = {
  changeTab,
  swiperChange,
  updateVisionTabsData
};
