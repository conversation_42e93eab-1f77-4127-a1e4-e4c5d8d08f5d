/**
 * 应用配置管理模块
 * 统一管理应用中的各种配置项，提高可维护性
 */

/**
 * 视觉处理相关配置
 */
const VISION_CONFIG = {
  // 光照阈值配置
  LIGHT_THRESHOLDS: {
    LOW_LIGHT: 70,      // 低光阈值，亮度值小于70时启用夜视
    HIGH_LIGHT: 210     // 强光阈值，亮度值大于210时启用缩瞳模式
  },
  
  // 默认视觉参数
  DEFAULT_VISION_PARAMS: {
    DOG: {
      brightness: 1.5,           // 亮度增强
      contrast: 1.3,             // 对比度
      resolutionFactor: 0.5,     // 辨析度因子
      antiAliasFactor: 0.3,      // 抗锯齿因子
      motionSensitivity: 3.0,    // 运动敏感度
      motionThreshold: 10.0,     // 运动阈值
      motionSizeThreshold: 10.0  // 运动物体大小阈值
    },
    CAT: {
      brightness: 1.6,           // 猫科动物亮度稍高
      contrast: 1.4,             // 对比度稍高
      resolutionFactor: 0.4,     // 辨析度稍低
      antiAliasFactor: 0.25,     // 抗锯齿因子
      motionSensitivity: 4.0,    // 运动敏感度更高
      motionThreshold: 8.0,      // 运动阈值更低
      motionSizeThreshold: 8.0   // 运动物体大小阈值更低
    },
    HUSKY: {
      brightness: 1.8,           // 哈士奇特殊亮度值
      contrast: 1.3,
      resolutionFactor: 0.5,
      antiAliasFactor: 0.3,
      motionSensitivity: 3.0,
      motionThreshold: 10.0,
      motionSizeThreshold: 10.0
    }
  },
  
  // 视觉模式配置
  VISION_MODES: [
    {
      id: 'dichromatic',
      name: '二色视觉',
      icon: '🎨',
      level: 1,
      description: '模拟宠物的色彩感知能力',
      includes: ['色彩转换'],
      features: { color: true, night: false, motion: false }
    },
    {
      id: 'acuity',
      name: '视力辨析度',
      icon: '👁️',
      level: 2,
      description: '模拟宠物的视觉清晰度',
      includes: ['模糊效果', '清晰度调整'],
      features: { color: false, night: false, motion: false }
    },
    {
      id: 'nightVision',
      name: '夜视能力',
      icon: '🌙',
      level: 3,
      description: '完整的宠物视觉体验',
      includes: ['二色视觉', '视力辨析度', '夜视增强'],
      features: { color: true, night: true, motion: false }
    },
    {
      id: 'motionVision',
      name: '运动视觉',
      icon: '🏃',
      level: 4,
      description: '模拟宠物对运动物体的敏感性',
      includes: ['运动检测', '运动增强', '所有基础功能'],
      features: { color: true, night: true, motion: true }
    }
  ]
};

/**
 * 相机相关配置
 */
const CAMERA_CONFIG = {
  // 分辨率选项
  RESOLUTION_OPTIONS: [
    { name: '高清(1920x1080)', value: 'high', frameSize: 'large', width: 1920, height: 1080 },
    { name: '标清(1280x720)', value: 'medium', frameSize: 'medium', width: 1280, height: 720 },
    { name: '流畅(640x480)', value: 'low', frameSize: 'small', width: 640, height: 480 }
  ],
  
  // 相机类型选项
  CAMERA_OPTIONS: [
    { 
      type: 'back', 
      name: '后置相机', 
      icon: '📷', 
      desc: '手机后置摄像头',
      available: true 
    },
    { 
      type: 'front', 
      name: '前置相机', 
      icon: '🤳', 
      desc: '手机前置摄像头',
      available: true 
    },
    { 
      type: 'cam', 
      name: '硬件相机', 
      icon: '📡', 
      desc: 'CAM硬件摄像设备',
      available: true 
    }
  ],
  
  // 默认设置
  DEFAULTS: {
    POSITION: 'back',
    RESOLUTION_INDEX: 1,  // 默认使用标清分辨率
    CAMERA_TYPE: 'back'
  }
};

/**
 * UI相关配置
 */
const UI_CONFIG = {
  // 标签页配置
  TABS: ['视觉特征', '感知能力', '爱宠养护'],
  
  // 主题配置
  THEMES: {
    THEME1: 'theme1',
    THEME2: 'theme2',
    THEME3: 'theme3'
  },
  
  // 动画配置
  ANIMATIONS: {
    TRANSITION_DURATION: 300,    // 过渡动画时长(ms)
    FADE_DURATION: 500,          // 淡入淡出时长(ms)
    SLIDE_DURATION: 400,         // 滑动动画时长(ms)
    BOUNCE_DURATION: 600         // 弹跳动画时长(ms)
  },
  
  // 提示显示时长
  TOAST_DURATION: {
    SHORT: 1500,
    MEDIUM: 2000,
    LONG: 3000
  }
};

/**
 * 性能相关配置
 */
const PERFORMANCE_CONFIG = {
  // 设备性能等级
  DEVICE_LEVELS: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high'
  },
  
  // FPS监控配置
  FPS_MONITOR: {
    UPDATE_INTERVAL: 1000,       // FPS更新间隔(ms)
    RESET_TIMEOUT: 2000          // 无帧时重置FPS的超时时间(ms)
  },
  
  // 内存监控配置
  MEMORY_MONITOR: {
    CHECK_INTERVAL: 5000,        // 内存检查间隔(ms)
    WARNING_THRESHOLD: 0.8,      // 内存警告阈值(80%)
    CRITICAL_THRESHOLD: 0.9      // 内存危险阈值(90%)
  }
};

/**
 * CAM硬件相机配置
 */
const CAM_CONFIG = {
  // 连接状态
  STATUS: {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    ERROR: 'error'
  },
  
  // 缓冲区配置
  BUFFER: {
    BUFFER1: 'buffer1',
    BUFFER2: 'buffer2'
  },
  
  // 连接超时配置
  TIMEOUTS: {
    CONNECTION: 10000,           // 连接超时(ms)
    FRAME_UPDATE: 5000           // 帧更新超时(ms)
  }
};

/**
 * 错误处理配置
 */
const ERROR_CONFIG = {
  // 错误类型
  TYPES: {
    CAMERA_ERROR: 'camera_error',
    WEBGL_ERROR: 'webgl_error',
    NETWORK_ERROR: 'network_error',
    PERMISSION_ERROR: 'permission_error'
  },
  
  // 重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,             // 最大重试次数
    DELAY: 1000,                 // 重试延迟(ms)
    BACKOFF_FACTOR: 2            // 退避因子
  }
};

/**
 * 获取指定品种的视觉参数
 * @param {string} breedName - 品种名称
 * @param {boolean} isCat - 是否为猫科动物
 * @returns {Object} 视觉参数
 */
function getVisionParamsForBreed(breedName, isCat = false) {
  // 哈士奇特殊处理
  if (breedName && breedName.includes('哈士奇')) {
    return Object.assign({}, VISION_CONFIG.DEFAULT_VISION_PARAMS.HUSKY);
  }

  // 根据动物类型返回默认参数
  if (isCat) {
    return Object.assign({}, VISION_CONFIG.DEFAULT_VISION_PARAMS.CAT);
  } else {
    return Object.assign({}, VISION_CONFIG.DEFAULT_VISION_PARAMS.DOG);
  }
}

/**
 * 获取视觉模式配置
 * @param {string} modeId - 模式ID
 * @returns {Object|null} 模式配置
 */
function getVisionModeConfig(modeId) {
  return VISION_CONFIG.VISION_MODES.find(mode => mode.id === modeId) || null;
}

/**
 * 获取相机分辨率配置
 * @param {number} index - 分辨率索引
 * @returns {Object|null} 分辨率配置
 */
function getResolutionConfig(index) {
  return CAMERA_CONFIG.RESOLUTION_OPTIONS[index] || null;
}

/**
 * 获取相机类型配置
 * @param {string} type - 相机类型
 * @returns {Object|null} 相机类型配置
 */
function getCameraTypeConfig(type) {
  return CAMERA_CONFIG.CAMERA_OPTIONS.find(option => option.type === type) || null;
}

/**
 * 验证配置值是否有效
 * @param {string} configType - 配置类型
 * @param {*} value - 配置值
 * @returns {boolean} 是否有效
 */
function validateConfig(configType, value) {
  switch (configType) {
    case 'brightness':
    case 'contrast':
      return typeof value === 'number' && value >= 0.5 && value <= 3.0;
    case 'resolutionFactor':
    case 'antiAliasFactor':
      return typeof value === 'number' && value >= 0.1 && value <= 1.0;
    case 'motionSensitivity':
      return typeof value === 'number' && value >= 0.1 && value <= 10.0;
    case 'motionThreshold':
      return typeof value === 'number' && value >= 1.0 && value <= 50.0;
    case 'motionSizeThreshold':
      return typeof value === 'number' && value >= 1.0 && value <= 100.0;
    default:
      return true;
  }
}

module.exports = {
  VISION_CONFIG,
  CAMERA_CONFIG,
  UI_CONFIG,
  PERFORMANCE_CONFIG,
  CAM_CONFIG,
  ERROR_CONFIG,
  getVisionParamsForBreed,
  getVisionModeConfig,
  getResolutionConfig,
  getCameraTypeConfig,
  validateConfig
};
