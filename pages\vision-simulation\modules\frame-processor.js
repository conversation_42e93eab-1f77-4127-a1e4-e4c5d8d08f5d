/**
 * 帧处理器模块
 * 负责处理相机帧数据和WebGL渲染相关的逻辑
 * 从主文件中提取帧处理相关的逻辑，提高代码可维护性
 */

// 导入相关模块
const visionManager = require('../utils/vision-manager');
const visionProcessor = require('../utils/vision-processor');
const webglRenderer = require('../utils/webgl-renderer');

// 全局变量
let lastFrameTime = 0;
let frameCount = 0;

/**
 * 处理相机帧数据（WebGL版本）
 * @param {Object} page - 页面实例
 * @param {Object} frame - 相机帧数据
 * @param {Object} webglContext - WebGL上下文
 */
function processFrameWebGL(page, frame, webglContext) {
  if (!frame || !webglContext || !page) {
    return;
  }
  
  try {
    // 保存最后一帧数据供其他地方使用
    page._lastFrame = frame;
    
    // 更新帧尺寸信息
    if (frame.width && frame.height) {
      page.setData({
        frame: {
          width: frame.width,
          height: frame.height
        }
      });
    }
    
    // 获取当前视觉参数
    const visionParams = page.data.dogVisionParams || {};
    const features = page.data.features || {};
    const currentVisionMode = page.data.currentVisionMode || 'nightVision';
    
    // 准备运动检测参数
    const motionParams = {
      sensitivity: visionParams.motionSensitivity || 3.0,
      threshold: visionParams.motionThreshold || 10.0,
      sizeThreshold: visionParams.motionSizeThreshold || 10.0,
      enabled: features.motion || false
    };
    
    // 检查参数是否发生变化，避免不必要的重新配置
    if (!page._lastMotionParams || 
        JSON.stringify(motionParams) !== JSON.stringify(page._lastMotionParams)) {
      console.log('运动检测参数已更新:', motionParams);
    }
    
    // 保存当前参数供下次比较
    page._lastMotionParams = { ...motionParams };
    
    // 根据当前视觉模式强制覆盖夜视和强光效果
    let forceDisableNightVision = false;
    let forceDisableBrightLight = false;
    
    // 如果当前模式是二色视觉或视力辨析度，强制禁用夜视和强光效果
    if (currentVisionMode === 'dichromatic' || currentVisionMode === 'acuity') {
      forceDisableNightVision = true;
      forceDisableBrightLight = true;
    }
    
    // 确定动物类型
    const animalType = features.isCat ? 'cat' : 'dog';
    
    // 处理帧数据
    const result = visionManager.processFrame(
      frame, 
      webglContext, 
      animalType, 
      motionParams, 
      forceDisableNightVision, 
      forceDisableBrightLight
    );
    
    // 更新页面数据
    if (result) {
      const updateData = {};
      
      // 更新亮度信息
      if (typeof result.averageBrightness === 'number') {
        updateData.averageBrightness = result.averageBrightness;
      }
      
      // 更新光照状态
      if (typeof result.isLowLight === 'boolean') {
        updateData.isLowLight = result.isLowLight;
      }
      
      if (typeof result.isBrightLight === 'boolean') {
        updateData.isBrightLight = result.isBrightLight;
      }
      
      // 更新亮像素比例
      if (typeof result.brightPixelRatio === 'number') {
        updateData.brightPixelRatio = result.brightPixelRatio;
        updateData.brightPixelRatioFormatted = (result.brightPixelRatio * 100).toFixed(1);
      }
      
      // 批量更新数据
      if (Object.keys(updateData).length > 0) {
        page.setData(updateData);
      }
      
      // 调试日志，帮助确认强光检测状态
      if (result.brightPixelRatio > 0.3) {
        console.log('强光检测状态:', {
          brightPixelRatio: result.brightPixelRatio,
          isBrightLight: result.isBrightLight,
          forceDisableBrightLight: forceDisableBrightLight
        });
      }
    }
    
    // 更新FPS
    updateFPS(page);
    
  } catch (error) {
    console.error('帧处理出错:', error);
    
    // 错误恢复：重置相关状态
    page.setData({
      cameraError: true,
      cameraErrorMsg: '视觉处理出错，请重试'
    });
  }
}

/**
 * 更新FPS显示
 * @param {Object} page - 页面实例
 */
function updateFPS(page) {
  const now = Date.now();
  frameCount++;
  
  // 每秒更新一次FPS
  if (now - lastFrameTime >= 1000) {
    const fps = Math.round(frameCount * 1000 / (now - lastFrameTime));
    
    page.setData({
      currentFPS: fps
    });
    
    frameCount = 0;
    lastFrameTime = now;
  }
}

/**
 * 启动FPS监控
 * @param {Object} page - 页面实例
 */
function startFPSMonitoring(page) {
  if (page._fpsUpdateInterval) {
    clearInterval(page._fpsUpdateInterval);
  }
  
  lastFrameTime = Date.now();
  frameCount = 0;
  
  // 每秒检查一次FPS
  page._fpsUpdateInterval = setInterval(() => {
    // 如果长时间没有帧更新，重置FPS为0
    const now = Date.now();
    if (now - lastFrameTime > 2000) {
      page.setData({ currentFPS: 0 });
      frameCount = 0;
      lastFrameTime = now;
    }
  }, 1000);
  
  console.log('FPS监控已启动');
}

/**
 * 停止FPS监控
 * @param {Object} page - 页面实例
 */
function stopFPSMonitoring(page) {
  if (page._fpsUpdateInterval) {
    clearInterval(page._fpsUpdateInterval);
    page._fpsUpdateInterval = null;
  }
  
  page.setData({ currentFPS: 0 });
  frameCount = 0;
  lastFrameTime = 0;
  
  console.log('FPS监控已停止');
}

/**
 * 启动帧处理
 * @param {Object} page - 页面实例
 */
function startFrameProcessing(page) {
  if (!page._webglInitialized) {
    console.warn('WebGL未初始化，无法启动帧处理');
    return;
  }
  
  page._frameProcessingActive = true;
  console.log('帧处理已启动');
}

/**
 * 暂停帧处理
 * @param {Object} page - 页面实例
 */
function pauseFrameProcessing(page) {
  page._frameProcessingActive = false;
  console.log('帧处理已暂停');
}

/**
 * 检查帧处理是否活跃
 * @param {Object} page - 页面实例
 * @returns {boolean} 是否活跃
 */
function isFrameProcessingActive(page) {
  return page._frameProcessingActive === true;
}

/**
 * 处理相机帧回调
 * @param {Object} page - 页面实例
 * @param {Object} frame - 相机帧数据
 */
function onCameraFrame(page, frame) {
  // 检查帧处理是否活跃
  if (!isFrameProcessingActive(page)) {
    return;
  }
  
  // 检查WebGL是否可用
  if (!page._webglContext || !page._webglInitialized) {
    return;
  }
  
  // 处理帧数据
  processFrameWebGL(page, frame, page._webglContext);
}

/**
 * 重置帧处理状态
 * @param {Object} page - 页面实例
 */
function resetFrameProcessing(page) {
  page._lastFrame = null;
  page._lastMotionParams = null;
  page._frameProcessingActive = false;
  
  // 重置FPS相关变量
  frameCount = 0;
  lastFrameTime = 0;
  
  console.log('帧处理状态已重置');
}

/**
 * 获取帧处理统计信息
 * @param {Object} page - 页面实例
 * @returns {Object} 统计信息
 */
function getFrameProcessingStats(page) {
  return {
    currentFPS: page.data.currentFPS || 0,
    frameCount: frameCount,
    lastFrameTime: lastFrameTime,
    isActive: isFrameProcessingActive(page),
    hasLastFrame: !!page._lastFrame,
    webglInitialized: !!page._webglInitialized
  };
}

module.exports = {
  processFrameWebGL,
  updateFPS,
  startFPSMonitoring,
  stopFPSMonitoring,
  startFrameProcessing,
  pauseFrameProcessing,
  isFrameProcessingActive,
  onCameraFrame,
  resetFrameProcessing,
  getFrameProcessingStats
};
