/**
 * 页面数据管理器
 * 负责管理页面的初始数据结构和数据更新操作
 * 从主文件中提取数据相关的逻辑，提高代码可维护性
 */

/**
 * 获取页面初始数据
 * @returns {Object} 页面初始数据对象
 */
function getInitialPageData() {
  return {
    breedName: "",
    breedDetails: {},
    currentView: 'dog',
    cameraPosition: 'back',
    averageBrightness: 0,
    isLowLight: false,
    isBrightLight: false,
    brightPixelRatio: 0,
    brightPixelRatioFormatted: '0.0',
    LOW_LIGHT_THRESHOLD: 70, // 低光阈值，亮度值小于70时启用夜视
    HIGH_LIGHT_THRESHOLD: 210, // 强光阈值，亮度值大于180时启用缩璞模式
    devicePerformance: 'medium', // 默认中等性能
    
    // 光照模式提示相关
    showBrightLightTip: false,
    showNightVisionTip: false,
    showMotionTip: false, // 运动视觉提示
    
    // 性能相关
    currentFPS: 0,
    frame: { width: 0, height: 0 },
    cameraLoading: true,
    cameraError: false,
    cameraErrorMsg: '',
    
    // 相机布局相关
    isAnalysisExpanded: false, // 分析区域是否展开
    showControls: true, // 控制面板是否显示，默认显示
    
    // 相机选择器相关
    showCameraSelector: false, // 是否显示相机选择器
    currentCameraType: 'back', // 当前相机类型: 'back', 'front', 'cam'
    cameraOptions: [
      { 
        type: 'back', 
        name: '后置相机', 
        icon: '📷', 
        desc: '手机后置摄像头',
        available: true 
      },
      { 
        type: 'front', 
        name: '前置相机', 
        icon: '🤳', 
        desc: '手机前置摄像头',
        available: true 
      },
      { 
        type: 'cam', 
        name: '硬件相机', 
        icon: '📡', 
        desc: 'CAM硬件摄像设备',
        available: true 
      }
    ],
    
    // 分辨率设置
    resolutionOptions: [
      { name: '高清(1920x1080)', value: 'high', frameSize: 'large', width: 1920, height: 1080 },
      { name: '标清(1280x720)', value: 'medium', frameSize: 'medium', width: 1280, height: 720 },
      { name: '流畅(640x480)', value: 'low', frameSize: 'small', width: 640, height: 480 }
    ],
    currentResolutionIndex: 1, // 默认使用标清分辨率
    showResolutionSelector: false, // 控制分辨率选择器显示
    
    // 视觉模式选择器
    showVisionModeSelector: false, // 控制视觉模式选择器显示
    currentVisionMode: 'nightVision', // 默认夜视能力模式，包含二色视觉和视力辨析度
    showVisionModeDetail: true, // 控制视觉模式详情显示
    
    // 功能开关
    features: {
      motion: false,
      night: true,  // 默认启用夜视功能
      color: true,  // 默认启用二色视觉
      isCat: false  // 默认为犬科动物
    }
  };
}

/**
 * 获取犬科视觉特性参数
 * @returns {Object} 犬科视觉参数对象
 */
function getDogVisionParams() {
  return {
    // 增强夜视能力参数
    brightness: 1.5,       // 亮度增强 - 提高夜视能力 (默认参数，哈士奇会单独设置为1.8)
    contrast: 1.3,         // 对比度 - 提高夜视能力
    // 辨析度参数
    resolutionFactor: 0.5, // 提高辨析度
    antiAliasFactor: 0.3,   // 视野因子 - 控制视野范围内外的清晰度差异
    // 运动视觉参数
    motionSensitivity: 3.0,  // 运动敏感度 - 控制运动检测的敏感程度
    motionThreshold: 10.0,   // 运动阈值 - 仅超过阈值的运动才会被检测到
    motionSizeThreshold: 10.0 // 运动物体大小阈值 - 控制可被检测到的运动物体大小
  };
}

/**
 * 获取原始犬科视觉参数（用于重置）
 * @returns {Object} 原始犬科视觉参数对象
 */
function getOriginalDogVisionParams() {
  return {
    resolutionFactor: 0.5,
    antiAliasFactor: 0.3, // 视野因子的默认值为0.3
    brightness: 1.5,
    contrast: 1.3,
    motionSensitivity: 3.0, // 犬科运动敏感度默认值
    motionThreshold: 10.0, // 犬科运动阈值默认值
    motionSizeThreshold: 10.0 // 犬科物体大小阈值默认值
  };
}

/**
 * 获取标签页数据
 * @returns {Object} 标签页相关数据
 */
function getTabsData() {
  return {
    // 标签页控制
    currentTab: 0,
    tabs: ['视觉特征', '感知能力', '爱宠养护'],
    // 按标签页分类的视觉特点
    visionTabs: {
      basicFeatures: {
        eyeColor: '琥珀色',
        eyeShape: '圆形',
        colorSystem: '双色视觉',
        visualAngle: '水平240°'
      },
      coreFeatures: {
        visualAcuity: '人类的1/4-1/5',
        nightVision: '人类的3-5倍',
        motionPerception: '优于人类2倍'
      },
      otherFeatures: {
        tearStains: '轻微'
      }
    },
    showVisionParams: false  // 控制视觉参数面板显示
  };
}

/**
 * 获取视觉模式配置
 * @returns {Array} 视觉模式配置数组
 */
function getVisionModes() {
  return [
    {
      id: 'dichromatic',
      name: '二色视觉',
      icon: '🎨',
      level: 1,
      description: '模拟宠物的色彩感知能力',
      includes: ['色彩转换'],
      details: '宠物只能看到蓝色和黄色，无法区分红色和绿色'
    },
    {
      id: 'acuity',
      name: '视力辨析度',
      icon: '👁️',
      level: 2,
      description: '模拟宠物的视觉清晰度',
      includes: ['模糊效果', '清晰度调整'],
      details: '宠物的视力约为人类的1/4到1/5，看到的世界相对模糊'
    },
    {
      id: 'nightVision',
      name: '夜视能力',
      icon: '🌙',
      level: 3,
      description: '完整的宠物视觉体验',
      includes: ['二色视觉', '视力辨析度', '夜视增强'],
      details: '综合模拟宠物在不同光线条件下的视觉效果'
    },
    {
      id: 'motionVision',
      name: '运动视觉',
      icon: '🏃',
      level: 4,
      description: '模拟宠物对运动物体的敏感性',
      includes: ['运动检测', '运动增强', '所有基础功能'],
      details: '宠物对运动物体特别敏感，能够快速捕捉到细微的移动'
    }
  ];
}

/**
 * 获取视觉模式详情配置
 * @returns {Object} 视觉模式详情对象
 */
function getVisionModeDetails() {
  return {
    dichromatic: {
      title: "二色视觉（基础层）",
      desc: "模拟犬科动物的二色视觉系统，主要感知蓝色和黄色光谱，对红绿色调区分能力较弱。犬科动物无法区分红色与绿色，但在蓝黄色调上有较好辨识能力。",
      includes: "仅二色视觉"
    },
    acuity: {
      title: "视力辨析（第2层）",
      desc: "模拟犬科动物视力辨析能力，约为人类的1/4-1/5，远处物体变得模糊。犬科动物近距离视力较好，但无法看清远处细节，无法阅读文字，但对移动物体敏感。",
      includes: "二色视觉 + 视力辨析"
    },
    nightVision: {
      title: "光感模式（第3层）",
      desc: "模拟犬科动物在低光环境中的出色视觉，包括亮度增强和对比度调整。犬科动物具有出色的夜间视觉能力，视网膜上的感光细胞比人类多2-3倍，能在几乎全黑环境中辨识物体。",
      includes: "二色视觉 + 视力辨析 + 光感模式"
    },
    motionVision: {
      title: "运动视觉（全功能）",
      desc: "模拟犬科动物对运动目标的敏锐感知能力，能迅速捕捉微小的动态变化。犬科动物视网膜上的感光细胞对移动物体极为敏感，即使在低光和远距离条件下，也能迅速察觉到细微的运动目标。",
      includes: "二色视觉 + 视力辨析 + 光感模式 + 运动视觉"
    }
  };
}

/**
 * 获取CAM硬件相机相关数据
 * @returns {Object} CAM相关数据对象
 */
function getCAMData() {
  return {
    camMode: false,                    // 是否启用CAM模式
    camStatus: 'disconnected',         // CAM连接状态: disconnected, connecting, connected, error
    camImageUrl: '',                   // CAM图片流URL
    camNextImageUrl: '',               // CAM下一帧URL
    camCurrentBuffer: 'buffer1',       // CAM当前显示缓冲区
    camIsLoading: false,               // CAM是否正在加载
    camErrorMsg: '',                   // CAM错误信息
    showVisionGuide: false,
    rememberVisionGuide: false
  };
}

/**
 * 更新页面数据
 * @param {Object} page - 页面实例
 * @param {Object} updateData - 要更新的数据
 */
function updatePageData(page, updateData) {
  if (page && typeof page.setData === 'function') {
    page.setData(updateData);
  }
}

/**
 * 获取当前页面数据的某个字段
 * @param {Object} page - 页面实例
 * @param {string} fieldPath - 字段路径，支持点号分隔的嵌套路径
 * @returns {*} 字段值
 */
function getPageDataField(page, fieldPath) {
  if (!page || !page.data) return undefined;
  
  const fields = fieldPath.split('.');
  let value = page.data;
  
  for (const field of fields) {
    if (value && typeof value === 'object' && field in value) {
      value = value[field];
    } else {
      return undefined;
    }
  }
  
  return value;
}

module.exports = {
  getInitialPageData,
  getDogVisionParams,
  getOriginalDogVisionParams,
  getTabsData,
  getVisionModes,
  getVisionModeDetails,
  getCAMData,
  updatePageData,
  getPageDataField
};
