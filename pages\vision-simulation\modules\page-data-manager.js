/**
 * 页面数据管理器
 * 负责管理页面的初始数据结构和数据更新操作
 * 从主文件中提取数据相关的逻辑，提高代码可维护性
 */

// 导入配置模块
const appConfig = require('../config/app-config');
const constants = require('../config/constants');

/**
 * 获取页面初始数据
 * @returns {Object} 页面初始数据对象
 */
function getInitialPageData() {
  return {
    breedName: "",
    breedDetails: {},
    currentView: constants.VISION_CONSTANTS.VIEW_TYPES.PET,
    cameraPosition: appConfig.CAMERA_CONFIG.DEFAULTS.POSITION,
    averageBrightness: 0,
    isLowLight: false,
    isBrightLight: false,
    brightPixelRatio: 0,
    brightPixelRatioFormatted: '0.0',
    LOW_LIGHT_THRESHOLD: appConfig.VISION_CONFIG.LIGHT_THRESHOLDS.LOW_LIGHT,
    HIGH_LIGHT_THRESHOLD: appConfig.VISION_CONFIG.LIGHT_THRESHOLDS.HIGH_LIGHT,
    devicePerformance: constants.STATE_CONSTANTS.PERFORMANCE_LEVELS.MEDIUM,
    
    // 光照模式提示相关
    showBrightLightTip: false,
    showNightVisionTip: false,
    showMotionTip: false, // 运动视觉提示
    
    // 性能相关
    currentFPS: 0,
    frame: { width: 0, height: 0 },
    cameraLoading: true,
    cameraError: false,
    cameraErrorMsg: '',
    
    // 相机布局相关
    isAnalysisExpanded: false, // 分析区域是否展开
    showControls: true, // 控制面板是否显示，默认显示
    
    // 相机选择器相关
    showCameraSelector: false,
    currentCameraType: appConfig.CAMERA_CONFIG.DEFAULTS.CAMERA_TYPE,
    cameraOptions: appConfig.CAMERA_CONFIG.CAMERA_OPTIONS,

    // 分辨率设置
    resolutionOptions: appConfig.CAMERA_CONFIG.RESOLUTION_OPTIONS,
    currentResolutionIndex: appConfig.CAMERA_CONFIG.DEFAULTS.RESOLUTION_INDEX,
    showResolutionSelector: false,
    
    // 视觉模式选择器
    showVisionModeSelector: false,
    currentVisionMode: constants.VISION_CONSTANTS.MODES.NIGHT_VISION,
    showVisionModeDetail: true,

    // 功能开关
    features: {
      motion: false,
      night: true,
      color: true,
      isCat: false
    }
  };
}

/**
 * 获取犬科视觉特性参数
 * @param {string} breedName - 品种名称
 * @param {boolean} isCat - 是否为猫科动物
 * @returns {Object} 视觉参数对象
 */
function getDogVisionParams(breedName = '', isCat = false) {
  return appConfig.getVisionParamsForBreed(breedName, isCat);
}

/**
 * 获取原始犬科视觉参数（用于重置）
 * @param {string} breedName - 品种名称
 * @param {boolean} isCat - 是否为猫科动物
 * @returns {Object} 原始视觉参数对象
 */
function getOriginalDogVisionParams(breedName = '', isCat = false) {
  return appConfig.getVisionParamsForBreed(breedName, isCat);
}

/**
 * 获取标签页数据
 * @returns {Object} 标签页相关数据
 */
function getTabsData() {
  return {
    // 标签页控制
    currentTab: constants.NUMERIC_CONSTANTS.DEFAULTS.TAB_INDEX,
    tabs: appConfig.UI_CONFIG.TABS,
    // 按标签页分类的视觉特点
    visionTabs: {
      basicFeatures: {
        eyeColor: '琥珀色',
        eyeShape: '圆形',
        colorSystem: '双色视觉',
        visualAngle: '水平240°'
      },
      coreFeatures: {
        visualAcuity: '人类的1/4-1/5',
        nightVision: '人类的3-5倍',
        motionPerception: '优于人类2倍'
      },
      otherFeatures: {
        tearStains: '轻微'
      }
    },
    showVisionParams: false  // 控制视觉参数面板显示
  };
}

/**
 * 获取视觉模式配置
 * @returns {Array} 视觉模式配置数组
 */
function getVisionModes() {
  return appConfig.VISION_CONFIG.VISION_MODES;
}

/**
 * 获取视觉模式详情配置
 * @returns {Object} 视觉模式详情对象
 */
function getVisionModeDetails() {
  return {
    dichromatic: {
      title: "二色视觉（基础层）",
      desc: "模拟犬科动物的二色视觉系统，主要感知蓝色和黄色光谱，对红绿色调区分能力较弱。犬科动物无法区分红色与绿色，但在蓝黄色调上有较好辨识能力。",
      includes: "仅二色视觉"
    },
    acuity: {
      title: "视力辨析（第2层）",
      desc: "模拟犬科动物视力辨析能力，约为人类的1/4-1/5，远处物体变得模糊。犬科动物近距离视力较好，但无法看清远处细节，无法阅读文字，但对移动物体敏感。",
      includes: "二色视觉 + 视力辨析"
    },
    nightVision: {
      title: "光感模式（第3层）",
      desc: "模拟犬科动物在低光环境中的出色视觉，包括亮度增强和对比度调整。犬科动物具有出色的夜间视觉能力，视网膜上的感光细胞比人类多2-3倍，能在几乎全黑环境中辨识物体。",
      includes: "二色视觉 + 视力辨析 + 光感模式"
    },
    motionVision: {
      title: "运动视觉（全功能）",
      desc: "模拟犬科动物对运动目标的敏锐感知能力，能迅速捕捉微小的动态变化。犬科动物视网膜上的感光细胞对移动物体极为敏感，即使在低光和远距离条件下，也能迅速察觉到细微的运动目标。",
      includes: "二色视觉 + 视力辨析 + 光感模式 + 运动视觉"
    }
  };
}

/**
 * 获取CAM硬件相机相关数据
 * @returns {Object} CAM相关数据对象
 */
function getCAMData() {
  return {
    camMode: false,
    camStatus: appConfig.CAM_CONFIG.STATUS.DISCONNECTED,
    camImageUrl: '',
    camNextImageUrl: '',
    camCurrentBuffer: appConfig.CAM_CONFIG.BUFFER.BUFFER1,
    camIsLoading: false,
    camErrorMsg: '',
    showVisionGuide: false,
    rememberVisionGuide: false
  };
}

/**
 * 更新页面数据
 * @param {Object} page - 页面实例
 * @param {Object} updateData - 要更新的数据
 */
function updatePageData(page, updateData) {
  if (page && typeof page.setData === 'function') {
    page.setData(updateData);
  }
}

/**
 * 获取当前页面数据的某个字段
 * @param {Object} page - 页面实例
 * @param {string} fieldPath - 字段路径，支持点号分隔的嵌套路径
 * @returns {*} 字段值
 */
function getPageDataField(page, fieldPath) {
  if (!page || !page.data) return undefined;
  
  const fields = fieldPath.split('.');
  let value = page.data;
  
  for (const field of fields) {
    if (value && typeof value === 'object' && field in value) {
      value = value[field];
    } else {
      return undefined;
    }
  }
  
  return value;
}

module.exports = {
  getInitialPageData,
  getDogVisionParams,
  getOriginalDogVisionParams,
  getTabsData,
  getVisionModes,
  getVisionModeDetails,
  getCAMData,
  updatePageData,
  getPageDataField
};
