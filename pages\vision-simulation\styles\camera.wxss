/**
 * 相机相关样式模块
 * 包含相机容器、视图区域、相机控制等样式
 */

/* 固定适度尺寸样式 */
.camera-container {
  flex: none;
  height: 70vh; /* 固定适度高度 */
  overflow: hidden;
  position: relative;
  z-index: 1;
  /* 保持现有样式 */
  transform: translateZ(0); /* 强制GPU渲染 */
  -webkit-transform: translateZ(0);
  perspective: 1000;
  -webkit-perspective: 1000;
}

/* 相机区域视图样式 - 优化版 */
.view-area {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  will-change: transform; /* 启用硬件加速 */
  z-index: 1;
  width: 100vw; /* 使用视口宽度单位 */
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
}

.vision-view {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 使用cover而非contain，避免出现黑边 */
  transform: none !important;
  transition: none !important; /* 移除过渡效果，减少卡顿 */
  will-change: transform; /* 启用硬件加速 */
  -webkit-transform: translateZ(0); /* 强制GPU渲染 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  top: 0;
  left: 0;
  width: 100vw; /* 使用视口宽度单位 */
  height: 100%;
  background: #000;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
  pointer-events: none;
}

/* 消除全屏模式特殊处理，保持一致性 */
.camera-container.fullscreen .view-area,
.camera-container.fullscreen .vision-view {
  /* 保持与普通模式相同的设置，减少重新渲染 */
}

/* 优化相机组件渲染 - 适用于所有模式 */
camera {
  transform: none !important;
  transition: none !important;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  z-index: 1;
}

/* 优化WebGL画布渲染 - 适用于所有模式 */
canvas {
  transform: none !important;
  transition: none !important;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  z-index: 1;
}

/* 标题栏样式 */
.vision-title {
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  background: rgba(66, 82, 128, 0.9); /* 更亮的标题栏背景 */
  backdrop-filter: blur(5px);
  color: #ffffff;
  z-index: 5;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 增加边框亮度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 浅色主题下的标题栏样式 */
.light-theme-title {
  background: rgba(250, 250, 250, 0.9) !important;
  color: #333333 !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/* 标题颜色区分 */
.human-title {
  color: var(--tertiary-color);
}

.dog-title {
  color: var(--primary-color);
}

/* 相机加载状态 */
.camera-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.camera-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40rpx;
  background: rgba(40, 60, 90, 0.9);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-loading-spinner {
  width: 50rpx;
  height: 50rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid rgba(255, 138, 91, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.camera-loading-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 600;
}

/* 相机错误状态 */
.camera-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 110;
}

.camera-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40rpx;
  background: rgba(20, 20, 20, 0.9);
  border-radius: 16rpx;
  backdrop-filter: blur(20rpx);
  border: 2px solid rgba(220, 20, 60, 0.3);
  max-width: 80%;
}

.camera-error-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  color: rgba(220, 20, 60, 0.9);
  animation: errorShake 0.5s ease-in-out;
}

.camera-error-text {
  color: rgba(220, 20, 60, 0.9);
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.camera-error-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 22rpx;
  margin-bottom: 30rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
  line-height: 1.4;
}

/* 相机信息显示 */
.camera-info {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: rgba(0, 0, 0, 0.6);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  z-index: 50;
  backdrop-filter: blur(5px);
}

.camera-fps {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: rgba(0, 0, 0, 0.6);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  z-index: 50;
  backdrop-filter: blur(5px);
}

/* 相机切换按钮 */
.camera-switch {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.9);
  z-index: 50;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.camera-switch:active {
  background: rgba(255, 138, 91, 0.3);
  border-color: rgba(255, 138, 91, 0.5);
  transform: scale(0.9);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .camera-container {
    height: 65vh;
  }
  
  .vision-title {
    font-size: 14px;
    height: 36px;
    line-height: 36px;
  }
  
  .camera-info,
  .camera-fps {
    font-size: 20rpx;
    padding: 6rpx 12rpx;
  }
  
  .camera-switch {
    width: 70rpx;
    height: 70rpx;
    font-size: 36rpx;
    bottom: 16rpx;
    right: 16rpx;
  }
}
