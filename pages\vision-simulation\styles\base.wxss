/**
 * 基础样式模块
 * 包含核心容器、导航栏、基础布局等样式
 */

/* 核心容器样式 */
.container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: var(--dark-bg);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
  transition: background 0.5s ease; /* 添加平滑过渡动画 */
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 导航栏样式 */
.navbar {
  height: 40px; /* 恢复导航栏高度为标准高度 */
  display: flex;
  align-items: center;
  padding: 0;
  background: rgba(66, 82, 128, 0.95); /* 更亮的导航栏背景 */
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 增加边框亮度 */
  transition: background 0.5s ease, border-color 0.5s ease; /* 添加平滑过渡动画 */
}

.navbar-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 36rpx;
  transition: all 0.3s ease;
}

.navbar-back:active {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: scale(0.9);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 0.5rpx;
  margin-right: 80rpx; /* 平衡左侧返回按钮的空间 */
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 浅色主题样式 */
.light-theme-content {
  background: rgba(250, 250, 250, 0.95);
}

.light-theme-content .navbar {
  background: rgba(250, 250, 250, 0.95);
  border-bottom-color: rgba(0, 0, 0, 0.08);
}

.light-theme-content .navbar-title {
  color: rgba(0, 0, 0, 0.9);
}

.light-theme-content .navbar-back {
  color: rgba(0, 0, 0, 0.7);
}

/* 加载状态样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-container.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(40, 60, 90, 0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid rgba(255, 138, 91, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.loading-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  line-height: 1.4;
}

/* 错误状态样式 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(20, 20, 20, 0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 2px solid rgba(220, 20, 60, 0.3);
  max-width: 80%;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: rgba(220, 20, 60, 0.9);
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-text {
  color: rgba(220, 20, 60, 0.9);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.error-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
  line-height: 1.4;
}

/* 通用按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 120rpx;
}

.btn-primary {
  background: rgba(255, 138, 91, 0.2);
  border-color: rgba(255, 138, 91, 0.4);
  color: rgba(255, 138, 91, 0.9);
}

.btn-primary:active {
  background: rgba(255, 138, 91, 0.3);
  transform: scale(0.95);
}

.btn-secondary {
  background: rgba(70, 130, 180, 0.2);
  border-color: rgba(70, 130, 180, 0.4);
  color: rgba(70, 130, 180, 0.9);
}

.btn-secondary:active {
  background: rgba(70, 130, 180, 0.3);
  transform: scale(0.95);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .navbar-title {
    font-size: 28rpx;
    margin-right: 60rpx;
  }
  
  .loading-content,
  .error-content {
    padding: 40rpx 30rpx;
    max-width: 90%;
  }
  
  .loading-text,
  .error-text {
    font-size: 28rpx;
  }
  
  .loading-desc,
  .error-desc {
    font-size: 22rpx;
  }
}
