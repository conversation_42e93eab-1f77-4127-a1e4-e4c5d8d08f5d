// 获取应用实例
const app = getApp();

// 导入核心模块
const webglRenderer = require('./utils/webgl-renderer');
const visionProcessor = require('./utils/vision-processor');
const uiController = require('./utils/ui-controller');
const utils = require('./utils/utils');
const cameraManager = require('./utils/camera-manager');
const visionManager = require('./utils/vision-manager');
const visionConfig = require('./utils/vision-config');
const tipsManager = require('./utils/tips-manager');
const camManager = require('./utils/cam-manager');

// 导入新的模块化组件
const pageDataManager = require('./modules/page-data-manager');
const lifecycleManager = require('./modules/lifecycle-manager');
const eventHandlers = require('./modules/event-handlers');
const frameProcessor = require('./modules/frame-processor');

// 全局变量，用于存储页面实例和WebGL上下文
let page = null;
let webglContext = null;
let cameraCtx = null;
let lastFrameTime = 0;
let frameCount = 0;
let fpsUpdateInterval = null;
let visionContext = null; // 视觉管理器上下文

Page({
  // 使用页面数据管理器获取初始数据
  data: {
    ...pageDataManager.getInitialPageData(),
    ...pageDataManager.getTabsData(),
    ...pageDataManager.getCAMData(),
    dogVisionParams: pageDataManager.getDogVisionParams(),
    originalDogVisionParams: pageDataManager.getOriginalDogVisionParams(),
    visionModes: pageDataManager.getVisionModes(),
    visionModeDetails: pageDataManager.getVisionModeDetails()
  },

  // 根据动物种类和品种设置视力清晰度因子
  setBreedVisualAcuity: function(breedName, animalType) {
    // 默认值
    let resolutionFactor = 0.5;
    
    // 根据动物类型和品种设置不同的清晰度因子
    if (animalType === 'cat') {
      // 猫科动物视力较差，默认设置为1/5 (20/100)
      resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.RESOLUTION_FACTOR;
      
      // 暹罗猫视力较好，设置为15/100
      if (breedName && breedName.includes('暹罗')) {
        resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.SIAMESE_RESOLUTION_FACTOR;
        console.log('设置暹罗猫的视力清晰度因子为:', resolutionFactor);
        
        // 调用暹罗猫的特殊夹视能力设置函数
        this.setSiameseNightVision();
      } else {
        console.log('设置猫科动物[' + breedName + ']的视力清晰度因子为:', resolutionFactor);
      }
    } else {
      // 犬科动物视力设置
      if (breedName) {
        if (breedName.includes('拉布拉多')) {
          // 拉布拉多与人比是20/20，设置为1
          resolutionFactor = 1.0;
        } else if (breedName.includes('金毛')) {
          // 金毛约20/60,设置为1/3
          resolutionFactor = 0.33;
        } else if (breedName.includes('边境') || breedName.includes('牧羊')) {
          // 边境牧羊20/50，设置2/5
          resolutionFactor = 0.4;
        } else if (breedName.includes('贵宾') || breedName.includes('柯基') || 
                  breedName.includes('比熊') || breedName.includes('哈士奇')) {
          // 贵宾、柯基、比熊和哈士奇20/75，设置4/15
          resolutionFactor = 0.27;
        } else if (breedName.includes('吉娃娃') || breedName.includes('法斗') || 
                  breedName.includes('法国')) {
          // 吉娃娃和法国法斗 20/80，设置1/4
          resolutionFactor = 0.25;
        }
        console.log('设置犬种[' + breedName + ']的视力清晰度因子为:', resolutionFactor);
      }
      
      // 设置哈士奇的夜视能力参数
      if (breedName && breedName.includes('哈士奇')) {
        this.setHuskyNightVision();
      }
    }
    
    // 输出调试信息，确认最终设置的视力清晰度因子
    console.log('最终设置的视力清晰度因子:', resolutionFactor, '动物类型:', animalType);
    
    // 强制设置数据，确保在UI上显示正确的值
    this.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor,
      'originalDogVisionParams.resolutionFactor': resolutionFactor
    });
    
    // 如果视觉管理器已初始化，更新其配置
    if (visionManager) {
      // 强制更新视觉管理器中的清晰度因子
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: resolutionFactor
      });
      
      console.log('更新视觉管理器的清晰度因子为:', resolutionFactor);
      
      // 强制重新渲染当前帧
      if (this._lastFrame && webglContext) {
        console.log('重新渲染帧以应用新的清晰度因子');
        this.processFrameWebGL(this._lastFrame);
      }
    } else {
      console.warn('视觉管理器尚未初始化，无法应用清晰度因子');
    }
    
    return resolutionFactor; // 返回设置的值，便于调用者使用
  },
  
  // 设置暹罗猫的特殊夹视能力参数
  setSiameseNightVision: function() {
    // 暹罗猫因为眼睛有轻微的视轴对位问题，夹视能力较弱
    const brightness = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_BRIGHTNESS; // 暹罗猫特殊亮度增强因子：1.5
    const contrast = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_CONTRAST;     // 暹罗猫特殊对比度因子：1.3
    
    console.log('设置暹罗猫的夹视能力参数 - 亮度增强:', brightness, '对比度:', contrast);
    
    // 更新UI显示的夹视参数
    this.setData({
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast,
      'originalDogVisionParams.brightness': brightness,
      'originalDogVisionParams.contrast': contrast
    });
    
    // 如果视觉管理器已初始化，更新其配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness,
        CONTRAST: contrast
      });
      
      // 如果有最后一帧，重新渲染
      if (this._lastFrame && webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
  },
  
  // 设置哈士奇的夹视能力参数
  setHuskyNightVision: function() {
    // 哈士奇的夹视能力要比其它品种的犬科动物要强
    // 设置与猫科动物相同的夹视参数
    const brightness = 2.0; // 与猫科动物相同的亮度增强
    const contrast = 1.5;   // 与猫科动物相同的对比度
    
    console.log('设置哈士奇的夹视能力参数 - 亮度增强:', brightness, '对比度:', contrast);
    
    // 更新UI显示的夹视参数
    this.setData({
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast,
      'originalDogVisionParams.brightness': brightness,
      'originalDogVisionParams.contrast': contrast
    });
    
    // 如果视觉管理器已初始化，更新其配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness,
        CONTRAST: contrast
      });
      
      // 如果有最后一帧，重新渲染
      if (this._lastFrame && webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
  },
  
  // 生命周期方法 - 使用模块化管理
  onLoad: function (options) {
    // 保存页面实例到全局变量
    page = this;

    // 初始化状态标记
    this._isPageLoaded = true;
    this._cameraInitialized = false;
    this._webglInitialized = false;
    this._pageHidden = false;

    // 初始化内存监控
    this._monitorMemoryUsage();

    // 使用生命周期管理器处理页面加载
    lifecycleManager.onLoad(this, options);
    
    if (selectedBreed) {
      this.setData({
        breedName: selectedBreed.name,
        breedDetails: selectedBreed.details
      });
      
      // 根据犬种设置视力清晰度因子
      this.setBreedVisualAcuity(selectedBreed.name);
      
      // 更新视觉标签页数据
      uiController.updateVisionTabsData(selectedBreed, this);
    }
    
    // 更新当前页面主题
    this.updateTheme();
    
    // 注册主题变化回调
    this.registerThemeCallback();
    
    // 为了避免和页面卸载时机冲突，使用setTimeout
    setTimeout(() => {
      if (this._isPageLoaded) {
        // 注册应用显示/隐藏事件监听
        wx.onAppShow(this._handleAppShow);
        wx.onAppHide(this._handleAppHide);
      }
    }, 500);
    
    // 性能优化：根据设备性能调整参数
    utils.adjustPerformanceSettings().then(result => {
      const { devicePerformance, skipFrames, deviceInfo } = result;
      
      console.log('设备性能级别:', devicePerformance, '设备信息:', deviceInfo);
      
      // 设置设备性能级别
      this.setData({ devicePerformance });
      
      // 设置视觉处理模块的设备性能级别
      visionProcessor.setDevicePerformance(devicePerformance);
      
      // 初始化WebGL - 相机初始化在WebGL初始化成功后进行
      this.initWebGL();
      
      // 启动FPS计数器
      frameProcessor.startFPSMonitoring(this);
      
      // 仅显示引导提示，不自动显示视觉模式选择器
      setTimeout(() => {
        if (this._isPageLoaded) {
          // 不再显示视觉引导提示
          // this.showVisionSelectGuide();
        }
      }, 1000);
      
      // 初始化CAM管理器
      this.initCamManager();
      
      // 初始化相机类型状态，确保界面显示与实际状态一致
      this.initializeCameraType();
    });
    
    console.log('页面初始化完成');
  },
  
  // 初始化相机类型状态
  initializeCameraType: function() {
    // 根据当前的camMode和cameraPosition设置currentCameraType
    let currentCameraType;
    
    if (this.data.camMode) {
      currentCameraType = 'cam';
    } else {
      currentCameraType = this.data.cameraPosition; // 'back' 或 'front'
    }
    
    console.log('初始化相机类型:', currentCameraType);
    
    this.setData({
      currentCameraType: currentCameraType
    });
  },
  
  // 显示"选择爱宠的视觉世界"引导提示 - 现已禁用
  showVisionSelectGuide: function() {
    // 不再显示引导提示，直接返回
    return;
    
    // 以下代码不再执行
    // 添加一个新的状态用于控制引导提示的显示
    this.setData({
      showVisionSelectGuide: true
    });
    
    // 创建一个动画效果，让提示引人注目
    let pulseAnimation = null;
    
    if (wx.createAnimation) {
      pulseAnimation = wx.createAnimation({
        duration: 1000,
        timingFunction: 'ease',
        delay: 0
      });
      
      // 执行循环动画
      const animateGuide = () => {
        pulseAnimation.scale(1.05).step();
        pulseAnimation.scale(1).step();
        
        this.setData({
          visionGuideAnimation: pulseAnimation.export()
        });
      };
      
      // 启动动画（只执行一次，不再循环）
      animateGuide();
    }
    
    // 3秒后自动隐藏引导提示
    setTimeout(() => {
      this.setData({
        showVisionSelectGuide: false
      });
    }, 3000);
  },
  
  // 处理小程序显示事件
  _handleAppShow: function() {
    console.log('小程序返回前台显示');
    this._pageHidden = false;
    
    // 如果页面已加载且WebGL已初始化，检查相机状态
    if (this._isPageLoaded && this._webglInitialized) {
      // 如果相机处于错误状态或未初始化，尝试重新初始化
      if (this.data.cameraError || !this._cameraInitialized) {
        console.log('检测到相机错误或未初始化，尝试重新初始化');
        // 延迟初始化，给系统时间准备
        setTimeout(() => {
          if (!this._pageHidden) {
            this.retryCameraInit();
          }
        }, 1000);
      } 
      // 新增：处理相机加载状态卡住的情况
      else if (this.data.cameraLoading) {
        console.log('检测到相机处于加载状态，可能是息屏后没有正确恢复，尝试重新初始化');
        // 延迟初始化，给系统时间准备
        setTimeout(() => {
          if (!this._pageHidden && this.data.cameraLoading) {
            // 如果仍然处于加载状态，强制重试
            this.retryCameraInit();
          }
        }, 1500);
      }
      // 新增：即使相机状态正常，也做一次帧检查
      else {
        // 添加一个延迟检查，如果长时间没有新帧，也重新初始化相机
        setTimeout(() => {
          if (!this._pageHidden && this._lastFrameCheckTime) {
            const now = Date.now();
            // 如果超过3秒没有收到新帧，认为相机可能已经停止工作
            if (now - this._lastFrameCheckTime > 3000) {
              console.log('检测到相机可能已停止工作（长时间未收到新帧），尝试重新初始化');
              this.retryCameraInit();
            }
          }
        }, 3000);
      }
    }
  },
  
  // 处理小程序隐藏事件
  _handleAppHide: function() {
    console.log('小程序进入后台');
    this._pageHidden = true;
  },
  
  // 启动FPS计数器 - 使用模块化管理
  startFPSCounter: function() {
    frameProcessor.startFPSMonitoring(this);
    },

  // 停止FPS计数器
  stopFPSCounter: function() {
    frameProcessor.stopFPSMonitoring(this);
  },

  // 启动帧处理
  startFrameProcessing: function() {
    frameProcessor.startFrameProcessing(this);
  },

  // 暂停帧处理
  pauseFrameProcessing: function() {
    frameProcessor.pauseFrameProcessing(this);
  },

  onReady: function() {
    console.log('页面就绪，准备完成');
    
    // 确保在页面完全加载后再次应用犬种特定的视力清晰度因子
    if (this.data.breedName) {
      console.log('onReady: 再次应用犬种特定的视力清晰度因子');
      this.setBreedVisualAcuity(this.data.breedName);
      
      // 如果视觉管理器已初始化，强制重新渲染
      if (visionContext && this._lastFrame) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
    
    // 添加额外的安全机制，确保相机初始化
    // 在某些情况下，WebGL初始化可能不会触发相机初始化
    setTimeout(() => {
      if (!this._cameraInitialized && !this.data.cameraLoading && !this._pageHidden) {
        console.log('安全检查：相机未初始化，尝试手动初始化');
        this.initCamera();
      }
    }, 2000);
    
    console.log('视觉模拟页面准备就绪');
  },

  onUnload: function() {
    console.log('视觉模拟页面卸载');
    
    // 重置授权重试计数器
    this._authRetryCount = 0;
    
    // 移除页面显示/隐藏事件监听
    wx.offAppShow(this._handleAppShow);
    wx.offAppHide(this._handleAppHide);
    
    // 移除主题变化回调
    this.unregisterThemeCallback();
    
    // 停止FPS计算
    if (fpsUpdateInterval) {
      clearInterval(fpsUpdateInterval);
      fpsUpdateInterval = null;
      console.log('清除FPS计数器');
    }
    
    if (this._retryTimer) {
      clearTimeout(this._retryTimer);
      this._retryTimer = null;
      console.log('清除重试定时器');
    }
    
    // 清除相机加载计时器
    if (this._cameraLoadingTimer) {
      clearTimeout(this._cameraLoadingTimer);
      this._cameraLoadingTimer = null;
      console.log('清除相机加载计时器');
    }
    
    // 清除所有安全计时器
    if (this._safetyTimers && this._safetyTimers.length > 0) {
      this._safetyTimers.forEach(timer => {
        clearTimeout(timer);
      });
      this._safetyTimers = [];
      console.log('清除所有安全计时器');
    }
    
    // 释放WebGL资源
    if (webglContext) {
      try {
        console.log('释放WebGL资源');
        webglRenderer.releaseWebGLResources(webglContext);
        webglContext = null;
      } catch (err) {
        console.error('释放WebGL资源失败:', err);
      }
    }
    
    // 重置状态标记
    this._isPageLoaded = false;
    this._cameraInitialized = false;
    this._webglInitialized = false;
    this._pageHidden = true;
    
    // 释放CAM资源
    try {
      camManager.releaseCamResources();
      console.log('CAM资源已释放');
    } catch (err) {
      console.error('释放CAM资源失败:', err);
    }
    
    // 重置CAM错误计数
    this._camErrorCount = 0;
    
    // 清除页面实例引用
    console.log('清除页面实例引用');
    page = null;
  },
  
  // 页面隐藏时处理
  onHide: function() {
    console.log('视觉模拟页面隐藏');
    
    // 清除相机加载计时器
    if (this._cameraLoadingTimer) {
      clearTimeout(this._cameraLoadingTimer);
      this._cameraLoadingTimer = null;
      console.log('清除相机加载计时器');
    }
    
    // 标记页面已隐藏
    this._pageHidden = true;
    
    // 页面隐藏时执行内存清理
    this._forceMemoryCleanup();
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 切换视角
  toggleView: function() {
    eventHandlers.toggleView(this);
  },

  // 切换运动视觉功能
  toggleMotionVision: function() {
    // 切换运动视觉状态
    const newMotionState = !this.data.features.motion;
    
    // 更新数据
    this.setData({
      'features.motion': newMotionState,
      // 如果启用运动视觉，则更新当前模式
      currentVisionMode: newMotionState ? 'motionVision' : 'nightVision'
    });
    
    // 如果启用运动视觉，显示提示
    if (newMotionState) {
      this.showMotionTip();
      
      // 确保运动视觉参数正确设置
      const animalType = this.data.features.isCat ? 'cat' : 'dog';
      console.log('当前动物类型:', animalType);
      
      // 修复运动视觉参数
      if (visionManager) {
        const fixedParams = visionManager.fixMotionVisionParameters();
        console.log('运动视觉参数已修复:', fixedParams);
        
        // 更新UI显示的参数和原始参数值
        if (animalType === 'cat') {
          this.setData({
            'dogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
            'dogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
            'dogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD,
            // 同时更新原始参数值，用于重置
            'originalDogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
            'originalDogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
            'originalDogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD
          });
        } else {
          this.setData({
            'dogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
            'dogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
            'dogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD,
            // 同时更新原始参数值，用于重置
            'originalDogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
            'originalDogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
            'originalDogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD
          });
        }
      }
    }
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
    
    // 添加触觉反馈，提升用户体验
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'medium'
      });
    }
    
    // 显示切换状态的反馈提示
    wx.showToast({
      title: newMotionState ? '运动视觉已开启' : '运动视觉已关闭',
      icon: 'none',
      duration: 1500
    });
  },
  
  // 显示运动视觉提示
  showMotionTip: function() {
    // 使用提示管理器显示运动视觉提示
    tipsManager.showMotionVisionTip(this);
  },
  
  // 关闭运动视觉提示
  closeMotionTip: function() {
    // 使用提示管理器关闭运动视觉提示
    tipsManager.closeMotionVisionTip(this);
  },

  // 运动视觉风格切换函数 - 简化版本，仅保留接口
  toggleMotionStyle: function() {
    wx.showToast({
      title: '已切换运动视觉风格',
      icon: 'none',
      duration: 1500
    });
  },

  // 初始化WebGL和视觉管理器
  initWebGL: async function() {
    try {
      // 获取动物类型
      const animalType = this.data.features.isCat ? 'cat' : 'dog';
      
      // 根据动物类型和品种设置视力清晰度因子
      if (this.data.breedName) {
        // 调用设置视力清晰度因子函数，传入品种和动物类型
        this.setBreedVisualAcuity(this.data.breedName, animalType);
        
        // 如果是犬科动物且是哈士奇，单独设置夜视能力参数
        if (animalType === 'dog' && this.data.breedName.includes('哈士奇')) {
          this.setHuskyNightVision();
        }
      } else {
        // 没有品种信息时，仍然调用设置视力清晰度函数，传入动物类型
        this.setBreedVisualAcuity('', animalType);
      }
      
      // 根据动物类型设置夜视参数
      let nightVisionConfig;
      
      if (animalType === 'cat') {
        // 猫科动物夜视参数
        nightVisionConfig = {
          CAT: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST
          },
          DOG: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST
          },
          LOW_LIGHT_THRESHOLD: this.data.LOW_LIGHT_THRESHOLD,
          ENABLED: this.data.features.night
        };
        
        // 更新UI显示的参数
        this.setData({
          'dogVisionParams.brightness': visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS,
          'dogVisionParams.contrast': visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST,
          // 设置视野因子为0.3
          'dogVisionParams.antiAliasFactor': visionConfig.VISUAL_ACUITY_CONFIG.VIEW_FIELD_FACTOR,
          'dogVisionParams.resolutionFactor': visionConfig.VISUAL_ACUITY_CONFIG.RESOLUTION_FACTOR,
          // 设置猫科动物的运动视觉参数
          'dogVisionParams.motionSensitivity': visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SENSITIVITY,
          'dogVisionParams.motionThreshold': visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_THRESHOLD,
          'dogVisionParams.motionSizeThreshold': visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SIZE_THRESHOLD
        });
        
        // 保存原始参数值，用于重置
        this.setData({
          'originalDogVisionParams.antiAliasFactor': visionConfig.VISUAL_ACUITY_CONFIG.VIEW_FIELD_FACTOR,
          'originalDogVisionParams.resolutionFactor': visionConfig.VISUAL_ACUITY_CONFIG.RESOLUTION_FACTOR,
          'originalDogVisionParams.motionSensitivity': visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SENSITIVITY,
          'originalDogVisionParams.motionThreshold': visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_THRESHOLD,
          'originalDogVisionParams.motionSizeThreshold': visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SIZE_THRESHOLD
        });
      } else {
        // 犬科动物夜视参数
        nightVisionConfig = {
          CAT: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST
          },
          DOG: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST
          },
          LOW_LIGHT_THRESHOLD: this.data.LOW_LIGHT_THRESHOLD,
          ENABLED: this.data.features.night
        };
        
        // 更新UI显示的参数
        this.setData({
          'dogVisionParams.brightness': visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS,
          'dogVisionParams.contrast': visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST,
          // 设置视野因子为0.3
          'dogVisionParams.antiAliasFactor': visionConfig.VISUAL_ACUITY_CONFIG.VIEW_FIELD_FACTOR,
          'dogVisionParams.resolutionFactor': visionConfig.VISUAL_ACUITY_CONFIG.RESOLUTION_FACTOR,
          // 确保运动视觉参数也被正确设置
          'dogVisionParams.motionSensitivity': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY,
          'dogVisionParams.motionThreshold': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD,
          'dogVisionParams.motionSizeThreshold': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD
        });
        
        // 保存原始参数值，用于重置
        this.setData({
          'originalDogVisionParams.antiAliasFactor': visionConfig.VISUAL_ACUITY_CONFIG.VIEW_FIELD_FACTOR,
          'originalDogVisionParams.resolutionFactor': visionConfig.VISUAL_ACUITY_CONFIG.RESOLUTION_FACTOR,
          'originalDogVisionParams.motionSensitivity': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY,
          'originalDogVisionParams.motionThreshold': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD,
          'originalDogVisionParams.motionSizeThreshold': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD
        });
      }
      
      // 在初始化前确保动物类型特定的清晰度因子已设置
      let breedSpecificResolutionFactor = this.data.dogVisionParams.resolutionFactor;
      
      // 根据动物类型和品种设置清晰度因子
      if (animalType === 'cat') {
        // 输出实际的品种名称，便于调试
        console.log('当前猫科动物品种名称:', this.data.breedName, '类型:', typeof this.data.breedName);
        
        // 猫科动物视力较差，默认设置为1/5 (20/100)
        breedSpecificResolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.RESOLUTION_FACTOR;
        
        // 暹罗猫视力较好，设置为15/100
        if (this.data.breedName) {
          // 尝试不同的匹配方式
          const breedName = String(this.data.breedName).trim();
          
          // 输出品种名称，便于调试
          console.log('处理后的品种名称:', breedName);
          
          // 直接判断是否为暹罗猫
          if (breedName === '暹罗') {
            breedSpecificResolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.SIAMESE_RESOLUTION_FACTOR; // 使用配置值
            console.log('设置暹罗猫的视力清晰度因子为:', breedSpecificResolutionFactor);
            
            // 更新UI显示的参数，确保参数设置面板显示正确的值
            this.setData({
              'dogVisionParams.resolutionFactor': breedSpecificResolutionFactor,
              'originalDogVisionParams.resolutionFactor': breedSpecificResolutionFactor
            });
            
            // 调用暹罗猫的特殊夹视能力设置函数
            this.setSiameseNightVision();
          }
        }
        
        // 更新UI显示的参数，确保参数设置面板显示正确的值
        this.setData({
          'dogVisionParams.resolutionFactor': breedSpecificResolutionFactor,
          'originalDogVisionParams.resolutionFactor': breedSpecificResolutionFactor
        });
        
        console.log('初始化前设置猫科动物[' + this.data.breedName + ']的视力清晰度因子为:', breedSpecificResolutionFactor);
      } else if (animalType === 'dog' && this.data.breedName) {
        // 犬科动物视力设置
        const breedName = this.data.breedName;
        if (breedName.includes('拉布拉多')) {
          breedSpecificResolutionFactor = 1.0;
        } else if (breedName.includes('金毛')) {
          breedSpecificResolutionFactor = 0.33;
        } else if (breedName.includes('边境') || breedName.includes('牧羊')) {
          breedSpecificResolutionFactor = 0.4;
        } else if (breedName.includes('贵宾') || breedName.includes('柯基') || 
                  breedName.includes('比熊') || breedName.includes('哈士奇')) {
          breedSpecificResolutionFactor = 0.27;
        } else if (breedName.includes('吉娃娃') || breedName.includes('法斗') || 
                  breedName.includes('法国')) {
          breedSpecificResolutionFactor = 0.25;
        }
        console.log('初始化前设置犬种[' + breedName + ']的视力清晰度因子为:', breedSpecificResolutionFactor);
      }
      
      // 初始化视觉管理器 - 使用await等待异步初始化完成
      visionContext = await visionManager.initVisionManager({
        // 初始化配置
        animalType: animalType,
        isLowLight: false,
        // 使用当前的视觉参数，并强制使用犬种特定的清晰度因子
        visualAcuity: {
          RESOLUTION_FACTOR: breedSpecificResolutionFactor, // 使用犬种特定的清晰度因子
          VIEW_FIELD_FACTOR: this.data.dogVisionParams.antiAliasFactor, // 使用新的参数名称
          ENABLED: true
        },
        nightVision: nightVisionConfig,
        dichromaticVision: {
          ENABLED: this.data.features.color
        }
      });
      
      // 更新UI显示的清晰度因子
      this.setData({
        'dogVisionParams.resolutionFactor': breedSpecificResolutionFactor,
        'originalDogVisionParams.resolutionFactor': breedSpecificResolutionFactor
      });
      
      // 输出初始化的参数值
      console.log('初始化完成，视力清晰度因子:', breedSpecificResolutionFactor);
      console.log('初始化视野因子值:', this.data.dogVisionParams.antiAliasFactor);
      
      // 获取WebGL上下文
      webglContext = visionContext.webglContext;
      
      if (webglContext) {
        console.log('初始化WebGL和视觉管理器成功');
        this._webglInitialized = true;
        
        // 如果页面没有隐藏，初始化相机
        if (!this._pageHidden) {
          // 添加延迟，给WebGL初始化完成留出时间
          setTimeout(() => {
            if (!this._pageHidden && !this._cameraInitialized) {
              console.log('在WebGL初始化成功后延迟初始化相机');
              this.initCamera();
            }
          }, 500);
        } else {
          console.log('页面当前处于隐藏状态，暂不初始化相机');
        }
      } else {
        throw new Error('无法获取WebGL上下文');
      }
    } catch (error) {
      console.error('初始化WebGL失败:', error);
      this.setData({ cameraError: true, cameraErrorMsg: '初始化图像处理失败' });
      wx.showToast({
        title: '初始化图像处理失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 初始化相机
  initCamera: function() {
    // CAM模式下不初始化手机相机
    if (this.data.camMode) {
      console.log('CAM模式下跳过手机相机初始化');
      return;
    }
    
    console.log('开始初始化相机，当前页面状态:', JSON.stringify({
      cameraLoading: this.data.cameraLoading,
      cameraError: this.data.cameraError,
      cameraInitializing: this.data.cameraInitializing
    }));
    
    // 先停止现有相机实例
    if (this.cameraCtx) {
      try {
        console.log('停止现有相机实例');
        cameraManager.stopCamera(this.cameraCtx);
        this.cameraCtx = null;
      } catch (err) {
        console.error('停止现有相机失败:', err);
      }
    }
    
    // 设置相机加载状态
    this.setData({
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: '',
      showCameraSettingBtn: false
    });
    
    console.log('调用相机管理器初始化相机');
    
    // 使用异步初始化相机，先检查权限再初始化
    // 注意：相机组件在WXML中设置为动态分辨率，根据用户选择自动切换
    // 默认使用标清分辨率1280*720，符合项目规范要求
    cameraManager.initCamera(this)
      .then(cameraContext => {
        if (cameraContext) {
          this.cameraCtx = cameraContext;
          
          // 记录相机分辨率信息
          const currentResolution = this.data.resolutionOptions[this.data.currentResolutionIndex];
          if (!this.data.frame.width || !this.data.frame.height) {
            this.setData({
              'frame.width': currentResolution.width,
              'frame.height': currentResolution.height
            });
          }
          
          // 全局变量兼容性
          cameraCtx = this.cameraCtx;
          
          // 设置相机初始化成功标记
          this._cameraInitialized = true;
          
          console.log('相机初始化成功，分辨率:', currentResolution.width, 'x', currentResolution.height);
          
          // 移除初始化成功的提示，不再显示"相机初始化成功"
        } else {
          console.error('相机初始化失败，返回空的相机上下文');
          this.handleCameraError({
            errCode: 'camera_context_null',
            errMsg: '初始化失败，返回空的相机上下文'
          });
        }
      })
      .catch(error => {
        console.error('相机初始化异常:', error);
        this.handleCameraError(error);
      });
  },

  // 使用WebGL处理帧 - 使用模块化管理
  processFrameWebGL: function(frame) {
    try {
      // 如果WebGL上下文未初始化或页面已卸载，则不处理
      if (!webglContext || !page || !visionContext) {
        console.warn('处理帧跳过: WebGL上下文未初始化或页面已卸载');
        return;
      }

      // CAM模式下，图片流处理方式不同，直接返回
      if (this.data.camMode) {
        console.log('CAM模式下不进行WebGL视觉处理');
        return;
      }

      // 使用帧处理器模块处理帧数据
      frameProcessor.processFrameWebGL(this, frame, webglContext);

    } catch (error) {
      console.error('帧处理出错:', error);

      // 错误恢复：重置相关状态
      this.setData({
        cameraError: true,
        cameraErrorMsg: '视觉处理出错，请重试'
      });
    }
      
      // 保存最后一帧，用于参数变化时重新渲染和保存图片
      if (frame && frame.data) {
        // 深拷贝帧数据以避免引用问题
        this._lastFrame = {
          data: new Uint8Array(frame.data),
          width: frame.width,
          height: frame.height
        };
        // 同时在WebGL上下文中也保存一份，确保保存图片时可用
        if (webglContext) {
          webglContext.rawFrame = this._lastFrame;
        }
      } else {
        console.warn('处理帧跳过: 无效的帧数据');
        return;
      }
      
      // 更新帧计数器
      frameCount++;
      
      // 获取当前选择的分辨率设置
      const currentResolution = this.data.resolutionOptions[this.data.currentResolutionIndex];
      const targetWidth = currentResolution.width;
      const targetHeight = currentResolution.height;
      
      // 更新帧尺寸信息，使用当前选择的分辨率
      if (this.data.frame.width !== targetWidth || this.data.frame.height !== targetHeight) {
        console.log('设置相机分辨率显示为', targetWidth, 'x', targetHeight, '，实际帧尺寸:', frame.width, 'x', frame.height);
        this.setData({
          frame: {
            width: targetWidth,
            height: targetHeight
          }
        });
      }
      
      // 使用视觉管理器处理帧
      const animalType = this.data.features.isCat ? 'cat' : 'dog';
      
      // 获取当前运动视觉参数
      const motionParams = {
        enabled: this.data.features.motion, // 传递运动视觉开关状态
        sensitivity: this.data.dogVisionParams.motionSensitivity,
        threshold: this.data.dogVisionParams.motionThreshold,
        sizeThreshold: this.data.dogVisionParams.motionSizeThreshold // 添加物体大小阈值参数
      };
      
      // 只在运动视觉开启或参数变化时输出调试日志
      if (this._lastMotionParams && (
          this._lastMotionParams.enabled !== motionParams.enabled ||
          this._lastMotionParams.sensitivity !== motionParams.sensitivity ||
          this._lastMotionParams.threshold !== motionParams.threshold ||
          this._lastMotionParams.sizeThreshold !== motionParams.sizeThreshold
        )) {
        console.log('运动视觉参数变化:', {
          '之前': this._lastMotionParams,
          '现在': motionParams
        });
      }
      
      // 保存当前参数供下次比较
      this._lastMotionParams = { ...motionParams };
      
      // 根据当前视觉模式强制覆盖夜视和强光效果
      let forceDisableNightVision = false;
      let forceDisableBrightLight = false;
      
      // 如果当前模式是二色视觉或视力辨析度，强制禁用夜视和强光效果
      if (this.data.currentVisionMode === 'dichromatic' || this.data.currentVisionMode === 'acuity') {
        forceDisableNightVision = true;
        forceDisableBrightLight = true;
      }
      
      const result = visionManager.processFrame(frame, webglContext, animalType, motionParams, forceDisableNightVision, forceDisableBrightLight);
      
      // 调试日志，帮助确认强光检测状态
      if (result.brightPixelRatio > 0.3) {
        console.log('强光检测状态:', {
          brightPixelRatio: result.brightPixelRatio,
          isBrightLight: result.isBrightLight,
          forceDisableBrightLight: forceDisableBrightLight
        });
      }
      
      // 更新亮度、低光状态和强光状态
      if (result.brightness !== this.data.averageBrightness || 
          result.isLowLight !== this.data.isLowLight || 
          result.isBrightLight !== this.data.isBrightLight || 
          result.brightPixelRatio !== this.data.brightPixelRatio) {
        
        // 先检测光照状态变化，然后再更新数据
        // 检测强光模式变化
        const brightLightChanged = result.isBrightLight !== this.data.isBrightLight;
        const enteringBrightLight = brightLightChanged && result.isBrightLight;
        
        // 检测夜视模式变化
        const lowLightChanged = result.isLowLight !== this.data.isLowLight;
        const enteringLowLight = lowLightChanged && result.isLowLight;
        
        // 计算格式化后的高亮像素比例
        const brightPixelRatio = result.brightPixelRatio || 0;
        const brightPixelRatioFormatted = (brightPixelRatio * 100).toFixed(1);
        
        // 更新数据
        this.setData({
          averageBrightness: result.brightness,
          isLowLight: result.isLowLight,
          isBrightLight: result.isBrightLight,
          brightPixelRatio: brightPixelRatio,
          brightPixelRatioFormatted: brightPixelRatioFormatted
        });
        
        // 显示相应提示(仅在进入模式时显示一次)
        if (enteringBrightLight) {
          // 进入强光模式，显示强光适应提示
          console.log('检测到进入强光模式，尝试显示强光适应提示');
          // 使用提示管理器确保提示不会重复显示
          tipsManager.showBrightLightTip(this);
        }
        
        if (enteringLowLight) {
          // 进入夜视模式，显示夜视能力提示
          console.log('检测到进入夜视模式，尝试显示夜视能力提示');
          // 使用提示管理器确保提示不会重复显示
          tipsManager.showNightVisionTip(this);
        }
      }
      
      // 如果相机加载中，标记为加载完成
      if (this.data.cameraLoading) {
        this.setData({ cameraLoading: false });
      }
    } catch (error) {
      console.error('处理帧错误:', error);
    }
  },

  // 相机错误处理
  handleCameraError: function(error) {
    console.log('处理相机错误:', error);
    utils.handleCameraError(error, this);
  },
  
  // 打式相机设置
  openCameraSetting: function() {
    console.log('用户点击打开设置按钮');
    utils.openCameraSetting(this);
  },
  
  // 重试初始化相机
  retryCameraInit: function() {
    console.log('重新初始化相机');
    // 清除任何现有的定时器
    if (this._retryTimer) {
      clearTimeout(this._retryTimer);
      this._retryTimer = null;
    }
    
    if (this._cameraLoadingTimer) {
      clearTimeout(this._cameraLoadingTimer);
      this._cameraLoadingTimer = null;
    }
    
    // 尝试先关闭任何可能存在的相机实例
    if (this.cameraCtx) {
      try {
        cameraManager.stopCamera(this.cameraCtx);
      } catch (err) {
        console.warn('停止旧相机实例时出错:', err);
      }
      this.cameraCtx = null;
    }
    
    // 重置相机状态
    this.setData({
      cameraError: false,
      cameraLoading: true,
      cameraInitializing: true,
      showCameraSettingBtn: false
    });
    
    // 增加小延迟再初始化，给系统时间释放资源
    this._retryTimer = setTimeout(() => {
      // 在初始化前再次检查页面是否隐藏
      if (this._pageHidden) {
        console.log('页面已隐藏，取消重新初始化相机');
        return;
      }
      
      console.log('执行相机重新初始化');
      this.initCamera();
      
      // 添加额外保障：如果相机初始化后5秒仍然处于加载状态，再次尝试
      const safetyTimer = setTimeout(() => {
        if (this.data && this.data.cameraLoading && !this._pageHidden) {
          console.log('安全检查：相机初始化5秒后仍在加载，再次尝试');
          // 递归调用，但最多尝试3次
          if (!this._retryCount) this._retryCount = 0;
          if (this._retryCount < 2) {
            this._retryCount++;
            this.retryCameraInit();
          } else {
            // 多次尝试失败，显示一个提示
            wx.showToast({
              title: '相机加载失败，请退出重试',
              icon: 'none',
              duration: 2000
            });
            // 自动重置重试计数
            setTimeout(() => {
              this._retryCount = 0;
            }, 5000);
          }
        } else {
          // 相机已正常加载或页面已隐藏，重置重试计数
          this._retryCount = 0;
        }
      }, 5000);
      
      // 当页面卸载时清除安全计时器
      if (this._safetyTimers) {
        this._safetyTimers.push(safetyTimer);
      } else {
        this._safetyTimers = [safetyTimer];
      }
    }, 800);
  },
  
  // 重置相机状态
  resetCameraState: function() {
    console.log('重置相机状态');
    if (this.cameraCtx) {
      cameraManager.stopCamera(this.cameraCtx);
      this.cameraCtx = null;
    }
    
    // 重置相机状态
    this.setData({
      cameraError: false,
      cameraLoading: false,
      cameraInitializing: false,
      showCameraSettingBtn: false
    });
  },

  // 显示强光适应提示
  showBrightLightTip: function() {
    // 使用提示管理器显示强光适应提示
    tipsManager.showBrightLightTip(this);
  },
  
  // 关闭强光适应提示
  closeBrightLightTip: function() {
    // 使用提示管理器关闭强光适应提示
    tipsManager.closeBrightLightTip(this);
  },
  
  // 显示夜视模式提示
  showNightVisionTip: function() {
    // 使用提示管理器显示夜视模式提示
    tipsManager.showNightVisionTip(this);
  },
  
  // 关闭夜视模式提示
  closeNightVisionTip: function() {
    // 使用提示管理器关闭夜视模式提示
    tipsManager.closeNightVisionTip(this);
  },
  
  // 更新主题
  updateTheme: function() {
    // 从全局应用实例中获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    const themeConfig = app.globalData.themeConfig || {};
    
    // 检查是否为浅色系主题
    const isLightTheme = currentTheme === 'theme6' || currentTheme === 'theme7' || currentTheme === 'theme8';
    
    // 应用主题到当前页面
    this.setData({
      currentTheme: currentTheme,
      isLightTheme: isLightTheme,
      themeStyle: {
        background: themeConfig.gradient || 'linear-gradient(145deg, #3D6B96, #5284B4)',
        backgroundDark: themeConfig.darkBg || '#3D6B96',
        cardBg: themeConfig.cardBg || 'rgba(70, 115, 160, 0.85)',
        textColor: themeConfig.textColor || '#ffffff'
      }
    });
    
    console.log('视觉模拟页面更新主题:', currentTheme);
  },
  
  // 触摸事件处理
  handleTouchStart: function(e) {
    // 记录触摸开始时间
    this.touchStartTime = Date.now();
  },

  handleTouchEnd: function(e) {
    // 如果是短按，切换视角
    if (Date.now() - this.touchStartTime < 300) {
      this.toggleView();
    }
  },
  
  // 切换分析区域展开/收起状态
  toggleAnalysisExpand: function() {
    const newState = !this.data.isAnalysisExpanded;
    this.setData({
      isAnalysisExpanded: newState
    });
    
    // 记录日志
    console.log('切换分析区域状态:', newState ? '展开' : '收起');
  },
  
  // 手势交互相关变量
  lastTapTime: 0, // 上次点击时间
  touchStartX: 0, // 触摸开始位置
  touchEndX: 0, // 触摸结束位置
  
  // 处理触摸开始事件
  handleTouchStart: function(e) {
    this.touchStartX = e.touches[0].clientX;
  },
  
  // 处理触摸结束事件 - 检测滑动手势
  handleTouchEnd: function(e) {
    this.touchEndX = e.changedTouches[0].clientX;
    
    // 计算滑动距离
    const swipeDistance = this.touchEndX - this.touchStartX;
    
    // 判断是否为有效滑动（距离超过50像素）
    if (Math.abs(swipeDistance) > 50) {
      if (swipeDistance > 0) {
        // 向右滑动，切换到爱宠视角
        if (this.data.currentView === 'human') {
          this.toggleView();
          // 显示切换提示 - 区分猫科和犬科动物
          let animalIcon = this.data.features.isCat ? '😺 ' : '🐶 ';
          wx.showToast({
            title: '切换到' + animalIcon + this.data.breedName + '视角',
            icon: 'none',
            duration: 1000
          });
        }
      } else {
        // 向左滑动，切换到人类视角
        if (this.data.currentView === 'dog') {
          this.toggleView();
          // 显示切换提示
          wx.showToast({
            title: '切换到人类视角',
            icon: 'none',
            duration: 1000
          });
        }
      }
    }
  },
  
  // 处理相机区域点击事件 - 双击控制显示/隐藏
  handleViewAreaTap: function() {
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - this.lastTapTime;
    
    // 判断是否为双击 (500毫秒内的两次点击)
    if (timeDiff < 500) {
      // 双击事件，切换控制面板显示/隐藏
      this.toggleControlPanel();
      this.lastTapTime = 0; // 重置点击时间，避免连续触发
    } else {
      // 记录单击时间
      this.lastTapTime = currentTime;
    }
  },
  
  // 切换控制面板显示/隐藏状态
  toggleControlPanel: function() {
    const newState = !this.data.showControls;
    this.setData({
      showControls: newState
    });
    
    // 记录日志
    console.log('切换控制面板状态:', newState ? '显示' : '隐藏');
  },

  // 显示/隐藏相机选择器
  toggleCameraSelector: function() {
    this.setData({
      showCameraSelector: !this.data.showCameraSelector
    });
  },
  
  // 选择相机类型
  selectCameraType: function(e) {
    const cameraType = e.currentTarget.dataset.type;
    
    // 如果选择的是当前相机类型，仅关闭选择器
    if (cameraType === this.data.currentCameraType) {
      this.toggleCameraSelector();
      return;
    }
    
    console.log('切换相机类型:', this.data.currentCameraType, '->', cameraType);
    
    // 更新当前相机类型
    this.setData({
      currentCameraType: cameraType,
      showCameraSelector: false
    });
    
    // 根据选择的相机类型执行相应操作
    this.switchToCamera(cameraType);
    
    // 添加触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },
  
  // 切换到指定相机
  switchToCamera: function(cameraType) {
    switch(cameraType) {
      case 'back':
      case 'front':
        // 切换到手机相机
        this.switchToPhoneCamera(cameraType);
        break;
      case 'cam':
        // 切换到硬件相机
        this.switchToHardwareCamera();
        break;
      default:
        console.error('未知的相机类型:', cameraType);
    }
  },
  
  // 切换到手机相机
  switchToPhoneCamera: function(position) {
    // 如果当前是CAM模式，先关闭CAM
    if (this.data.camMode) {
      this.disableCamMode();
    }
    
    // 先停止现有相机，避免资源冲突
    if (this.cameraCtx) {
      const cameraManager = require('./utils/camera-manager');
      cameraManager.stopCamera(this.cameraCtx);
      this.cameraCtx = null;
    }
    
    // 更新相机位置
    this.setData({
      cameraPosition: position,
      cameraLoading: true,
      // 重置低光状态，避免切换时的状态混乱
      isLowLight: false,
      averageBrightness: 0
    });
    
    // 等待数据更新完成后再初始化相机
    setTimeout(() => {
      // 重新初始化相机
      this.initCamera();
      
      // 显示切换提示
      const positionText = position === 'back' ? '后置相机' : '前置相机';
      wx.showToast({
        title: `已切换到${positionText}`,
        icon: 'none',
        duration: 1500
      });
    }, 100);
  },
  
  // 切换到硬件相机
  switchToHardwareCamera: function() {
    console.log('切换到硬件相机');
    this.enableCamMode();
  },

  // 显示/隐藏视觉参数调整面板
  toggleVisionParams: function() {
    // 在显示参数面板前，确保同步最新的品种特定参数
    if (!this.data.showVisionParams) {
      // 即将显示参数面板，同步品种特定参数到UI
      const breedName = this.data.breedName;
      if (breedName) {
        let resolutionFactor = this.data.originalDogVisionParams.resolutionFactor;
        
        // 根据品种设置视力清晰度
        if (this.data.features.isCat) {
          // 猫科动物处理逻辑
          if (breedName === '暹罗') {
            resolutionFactor = 0.15; // 暹罗猫特殊视力清晰度因子
          } else {
            resolutionFactor = 0.2; // 猫科视力清晰度因子
          }
        } else {
          // 犬科动物处理逻辑
          if (breedName.includes('拉布拉多')) {
            resolutionFactor = 1.0;
          } else if (breedName.includes('金毛')) {
            resolutionFactor = 0.33;
          } else if (breedName.includes('边境') || breedName.includes('牧羊')) {
            resolutionFactor = 0.4;
          } else if (breedName.includes('贵宾') || breedName.includes('柯基') || 
                    breedName.includes('比熊') || breedName.includes('哈士奇')) {
            resolutionFactor = 0.27;
          } else if (breedName.includes('吉娃娃') || breedName.includes('法斗') || 
                    breedName.includes('法国')) {
            resolutionFactor = 0.25;
          }
        }
        
        // 确保UI显示正确的参数值
        this.setData({
          'dogVisionParams.resolutionFactor': resolutionFactor
        });
        
        console.log('参数面板显示前同步品种[' + breedName + ']的视力清晰度参数:', resolutionFactor);
      }
    }
    
    // 切换参数面板的显示状态
    this.setData({
      showVisionParams: !this.data.showVisionParams
    });
  },
  
  // 显示/隐藏分辨率选择器
  toggleResolutionSelector: function() {
    this.setData({
      showResolutionSelector: !this.data.showResolutionSelector
    });
  },
  
  // 显示/隐藏视觉模式选择器
  toggleVisionModeSelector: function() {
    const newState = !this.data.showVisionModeSelector;
    
    this.setData({
      showVisionModeSelector: newState,
      showVisionModeDetail: newState, // 同步显示模式详情
      // 隐藏视觉选择引导提示
      showVisionSelectGuide: false
    });
    
    // 添加触觉反馈
    if (newState && wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },
  
  // 防止触摸事件穿透
  preventTouchMove: function(e) {
    // 阻止触摸事件传播
    return false;
  },
  
  // 选择视觉模式
  selectVisionMode: function(e) {
    const mode = e.currentTarget.dataset.mode;
    
    // 如果点击当前模式，仅关闭选择器
    if (mode === this.data.currentVisionMode) {
      this.toggleVisionModeSelector();
      return;
    }
    
    // 添加触觉反馈
    if (wx.vibrateShort) {
      // 根据不同模式提供略微不同的触觉反馈
      switch(mode) {
        case 'dichromatic':
          wx.vibrateShort({ type: 'light' });
          break;
        case 'acuity':
          wx.vibrateShort({ type: 'medium' });
          break;
        case 'nightVision':
          wx.vibrateShort({ type: 'medium' });
          break;
        case 'motionVision':
          wx.vibrateShort({ type: 'heavy' });
          break;
        default:
          wx.vibrateShort({ type: 'medium' });
      }
    }
    
    // 根据选择的模式设置相应的特性
    let features = { ...this.data.features };
    
    // 根据视觉模式的层级关系设置特性
    switch(mode) {
      case 'dichromatic':
        // 仅启用二色视觉，禁用视力辨析、夜视和运动视觉
        features.color = true;
        features.night = false;
        features.motion = false;
        
        // 更新视觉管理器配置 - 禁用视力辨析度、夜视和运动视觉
        if (visionManager) {
          // 启用二色视觉
          visionManager.updateDichromaticVisionConfig({
            ENABLED: true
          });
          
          // 禁用视力辨析度 - 设置最高清晰度
          visionManager.updateVisualAcuityConfig({
            RESOLUTION_FACTOR: 1.0,  // 最高清晰度，禁用模糊效果
            VIEW_FIELD_FACTOR: 0.1   // 最低视野因子，全视野清晰
          });
          
          // 禁用夜视能力
          visionManager.updateNightVisionConfig({
            ENABLED: false
          });
          
          // 禁用运动视觉
          visionManager.updateMotionVisionConfig({
            ENABLED: false
          });
        }
        break;
        
      case 'acuity':
        // 启用二色视觉和视力辨析度，禁用夜视和运动视觉
        features.color = true;
        features.night = false;
        features.motion = false;
        
        // 更新视觉管理器配置 - 启用视力辨析度，禁用夜视
        if (visionManager) {
          // 启用二色视觉
          visionManager.updateDichromaticVisionConfig({
            ENABLED: true
          });
          
          // 启用视力辨析度 - 恢复默认设置
          visionManager.updateVisualAcuityConfig({
            RESOLUTION_FACTOR: this.data.originalDogVisionParams.resolutionFactor,  // 恢复默认辨析度
            VIEW_FIELD_FACTOR: this.data.originalDogVisionParams.antiAliasFactor   // 恢复默认视野因子
          });
          
          // 禁用夜视能力
          visionManager.updateNightVisionConfig({
            ENABLED: false
          });
          
          // 禁用运动视觉
          visionManager.updateMotionVisionConfig({
            ENABLED: false
          });
        }
        break;
        
      case 'nightVision':
        // 启用二色视觉、视力辨析度和夜视能力，禁用运动视觉
        features.color = true;
        features.night = true;
        features.motion = false;
        
        // 更新视觉管理器配置 - 启用二色视觉、视力辨析度和夜视能力
        if (visionManager) {
          // 启用二色视觉
          visionManager.updateDichromaticVisionConfig({
            ENABLED: true
          });
          
          // 启用视力辨析度 - 恢复默认设置
          visionManager.updateVisualAcuityConfig({
            RESOLUTION_FACTOR: this.data.originalDogVisionParams.resolutionFactor,  // 恢复默认辨析度
            VIEW_FIELD_FACTOR: this.data.originalDogVisionParams.antiAliasFactor   // 恢复默认视野因子
          });
          
          // 启用夜视能力
          visionManager.updateNightVisionConfig({
            ENABLED: true
          });
          
          // 禁用运动视觉
          visionManager.updateMotionVisionConfig({
            ENABLED: false
          });
        }
        break;
        
      case 'motionVision':
        // 启用所有模式，包括运动视觉
        features.color = true;
        features.night = true;
        features.motion = true;
        
        // 更新视觉管理器配置 - 启用所有视觉效果
        if (visionManager) {
          // 启用二色视觉
          visionManager.updateDichromaticVisionConfig({
            ENABLED: true
          });
          
          // 启用视力辨析度 - 恢复默认设置
          visionManager.updateVisualAcuityConfig({
            RESOLUTION_FACTOR: this.data.originalDogVisionParams.resolutionFactor,  // 恢复默认辨析度
            VIEW_FIELD_FACTOR: this.data.originalDogVisionParams.antiAliasFactor   // 恢复默认视野因子
          });
          
          // 启用夜视能力
          visionManager.updateNightVisionConfig({
            ENABLED: true
          });
          
          // 启用运动视觉
          visionManager.updateMotionVisionConfig({
            ENABLED: true
          });
          
          // 确保运动视觉参数正确设置
          const animalType = this.data.features.isCat ? 'cat' : 'dog';
          console.log('当前动物类型:', animalType);
          
          // 修复运动视觉参数
          const fixedParams = visionManager.fixMotionVisionParameters();
          console.log('运动视觉参数已修复:', fixedParams);
          
          // 更新UI显示的参数和原始参数值
          if (animalType === 'cat') {
            this.setData({
              'dogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
              'dogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
              'dogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD,
              // 同时更新原始参数值，用于重置
              'originalDogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
              'originalDogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
              'originalDogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD
            });
          } else {
            this.setData({
              'dogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
              'dogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
              'dogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD,
              // 同时更新原始参数值，用于重置
              'originalDogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
              'originalDogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
              'originalDogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD
            });
          }
        }
        
        // 显示运动视觉提示
        this.showMotionTip();
        
        // 添加触觉反馈，提升用户体验
        if (wx.vibrateShort) {
          wx.vibrateShort({
            type: 'medium'
          });
        }
        break;
    }
    
    // 更新数据状态
    this.setData({
      currentVisionMode: mode,
      features: features,
      // 隐藏视觉选择引导提示
      showVisionSelectGuide: false
    });
    
    console.log('切换视觉模式为:', mode, ', 特性状态:', features);
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
    
    // 0.8秒后关闭选择器
    setTimeout(() => {
      this.toggleVisionModeSelector();
    }, 800);
  },
  
  // 获取模式名称
  getModeName: function(mode) {
    switch(mode) {
      case 'dichromatic': return '二色视觉';
      case 'acuity': return '视力辨析';
      case 'nightVision': return '光感模式';
      case 'motionVision': return '运动视觉';
      default: return '';
    }
  },
  
  // 显示运动视觉提示
  showMotionVisionTip: function() {
    // 使用提示管理器显示运动视觉提示
    tipsManager.showMotionVisionTip(this);
  },
  
  // 关闭运动视觉提示
  closeMotionTip: function() {
    // 使用提示管理器关闭运动视觉提示
    tipsManager.closeMotionVisionTip(this);
  },
  
  // 切换运动视觉
  toggleMotionVision: function() {
    // 切换运动视觉状态
    const newMotionState = !this.data.features.motion;
    
    // 更新数据
    this.setData({
      'features.motion': newMotionState,
      // 如果启用运动视觉，则更新当前模式
      currentVisionMode: newMotionState ? 'motionVision' : 'nightVision'
    });
    
    // 如果启用运动视觉，显示提示
    if (newMotionState) {
      this.showMotionTip();
      
      // 确保运动视觉参数正确设置
      const animalType = this.data.features.isCat ? 'cat' : 'dog';
      console.log('当前动物类型:', animalType);
      
      // 修复运动视觉参数
      if (visionManager) {
        const fixedParams = visionManager.fixMotionVisionParameters();
        console.log('运动视觉参数已修复:', fixedParams);
        
        // 更新UI显示的参数和原始参数值
        if (animalType === 'cat') {
          this.setData({
            'dogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
            'dogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
            'dogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD,
            // 同时更新原始参数值，用于重置
            'originalDogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
            'originalDogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
            'originalDogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD
          });
        } else {
          this.setData({
            'dogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
            'dogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
            'dogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD,
            // 同时更新原始参数值，用于重置
            'originalDogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
            'originalDogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
            'originalDogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD
          });
        }
      }
    }
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
    
    // 添加触觉反馈，提升用户体验
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'medium'
      });
    }
  },
  
  // 切换分辨率
  changeResolution: function(e) {
    const index = e.currentTarget.dataset.index;
    const currentOption = this.data.resolutionOptions[index];
    
    // 如果选择的是当前分辨率，则仅关闭选择器
    if (index === this.data.currentResolutionIndex) {
      this.toggleResolutionSelector();
      return;
    }
    
    // 设置加载状态
    this.setData({
      cameraLoading: true,
      currentResolutionIndex: index,
      showResolutionSelector: false,
      frame: {
        width: currentOption.width,
        height: currentOption.height
      }
    });
    
    console.log('切换分辨率为:', currentOption.name, currentOption.width + 'x' + currentOption.height);
    
    // 重新初始化相机
    this.reinitializeCamera();
  },
  
  // 重新初始化相机
  reinitializeCamera: function() {
    // 先停止现有相机
    if (this.cameraCtx) {
      cameraManager.stopCamera(this.cameraCtx);
      this.cameraCtx = null;
    }
    
    // 等待短暂停后再初始化相机，确保设置生效
    setTimeout(() => {
      this.initCamera();
    }, 300);
  },

  // 清晰度因子滑块变化处理
  onResolutionChange: function(e) {
    const resolutionFactor = e.detail.value;
    
    // 更新数据
    this.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor
    });
    
    // 更新视觉管理器中的视力辨析度配置
    visionManager.updateVisualAcuityConfig({
      RESOLUTION_FACTOR: resolutionFactor
    });
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },

  // 视野因子滑块变化处理
  onAntiAliasChange: function(e) {
    const viewFieldFactor = e.detail.value;
    
    // 更新数据
    this.setData({
      'dogVisionParams.antiAliasFactor': viewFieldFactor
    });
    
    // 更新视觉管理器中的视野因子配置
    visionManager.updateVisualAcuityConfig({
      VIEW_FIELD_FACTOR: viewFieldFactor
    });
    
    // 在控制台输出当前视野因子值，便于调试
    console.log('视野因子已更新为:', viewFieldFactor);
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },
  
  // 亮度增强滑块变化处理
  onBrightnessChange: function(e) {
    eventHandlers.onBrightnessChange(this, e);
  },
    
    // 更新视觉管理器中的亮度配置
    if (animalType === 'cat') {
      visionManager.updateNightVisionConfig({
        CAT: {
          BRIGHTNESS: brightness
        }
      });
    } else {
      visionManager.updateNightVisionConfig({
        DOG: {
          BRIGHTNESS: brightness
        }
      });
    }
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },

  // 对比度滑块变化处理
  onContrastChange: function(e) {
    eventHandlers.onContrastChange(this, e);
      'dogVisionParams.contrast': contrast
    });
    
    // 获取当前动物类型
    const animalType = this.data.features.isCat ? 'cat' : 'dog';
    
    // 更新视觉管理器中的对比度配置
    if (animalType === 'cat') {
      visionManager.updateNightVisionConfig({
        CAT: {
          CONTRAST: contrast
        }
      });
    } else {
      visionManager.updateNightVisionConfig({
        DOG: {
          CONTRAST: contrast
        }
      });
    }
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },
  
  // 运动敏感度滑块变化处理
  onMotionSensitivityChange: function(e) {
    const value = e.detail.value;
    
    // 更新数据
    this.setData({
      'dogVisionParams.motionSensitivity': value
    });
    
    // 获取当前动物类型
    const animalType = this.data.features.isCat ? 'cat' : 'dog';
    
    // 更新视觉管理器中的运动敏感度配置
    const motionConfig = {};
    if (animalType === 'cat') {
      motionConfig.CAT = {
        MOTION_SENSITIVITY: value
      };
    } else {
      motionConfig.DOG = {
        MOTION_SENSITIVITY: value
      };
    }
    
    // 调用视觉管理器的updateMotionVisionConfig方法更新参数
    visionManager.updateMotionVisionConfig(motionConfig);
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },
  
  // 运动阈值滑块变化处理
  onMotionThresholdChange: function(e) {
    const value = e.detail.value;
    
    // 更新数据
    this.setData({
      'dogVisionParams.motionThreshold': value
    });
    
    // 获取当前动物类型
    const animalType = this.data.features.isCat ? 'cat' : 'dog';
    
    // 更新视觉管理器中的运动阈值配置
    const motionConfig = {};
    if (animalType === 'cat') {
      motionConfig.CAT = {
        MOTION_THRESHOLD: value
      };
    } else {
      motionConfig.DOG = {
        MOTION_THRESHOLD: value
      };
    }
    
    // 调用视觉管理器的updateMotionVisionConfig方法更新参数
    visionManager.updateMotionVisionConfig(motionConfig);
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },
  
  // 运动物体大小阈值滑块变化处理
  onMotionSizeThresholdChange: function(e) {
    const value = e.detail.value;
    
    // 更新数据
    this.setData({
      'dogVisionParams.motionSizeThreshold': value
    });
    
    // 获取当前动物类型
    const animalType = this.data.features.isCat ? 'cat' : 'dog';
    
    // 更新视觉管理器中的运动物体大小阈值配置
    const motionConfig = {};
    if (animalType === 'cat') {
      motionConfig.CAT = {
        MOTION_SIZE_THRESHOLD: value
      };
    } else {
      motionConfig.DOG = {
        MOTION_SIZE_THRESHOLD: value
      };
    }
    
    // 调用视觉管理器的updateMotionVisionConfig方法更新参数
    visionManager.updateMotionVisionConfig(motionConfig);
    
    // 如果有最后一帧，重新渲染
    if (this._lastFrame && webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }
  },

  // 事件处理方法 - 使用模块化管理
  changeTab: function(e) {
    eventHandlers.changeTab(this, e);
  },

  swiperChange: function(e) {
    eventHandlers.swiperChange(this, e);
  },

  // 运动预设循环切换函数 - 简化版本，仅保留接口
  nextMotionPreset: function() {
    wx.showToast({
      title: '已切换运动预设',
      icon: 'none',
      duration: 1500
    });
  },

  // 保存爱宠视角图片
  savePetVisionImage: function() {
    const that = this; // 保存 this 指向
    
    // 初始化授权重试计数器
    this._authRetryCount = 0;
    
    // 检查当前是否为爱宠视角
    if (this.data.currentView !== 'dog') {
      wx.showToast({
        title: '请切换到爱宠视角',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    // 检查WebGL上下文是否存在 - 使用更宽松的检查条件
    if (!webglContext || !webglContext.gl) {
      console.log('尝试保存图片，但WebGL未完全初始化:', webglContext);
      
      // 尝试初始化WebGL上下文
      if (this._lastFrame) {
        console.log('发现缓存的帧数据，尝试重新初始化WebGL');
        this.initWebGL();
        
        // 给用户一个提示，请稍后再试
        wx.showToast({
          title: '正在准备图像，请稍后再试',
          icon: 'none',
          duration: 1500
        });
        return;
      }
      
      wx.showToast({
        title: 'WebGL未初始化，请稍后再试',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    // 检查Canvas节点
    if (!webglContext.canvasNode) {
      console.log('尝试修复Canvas节点缺失问题');
      
      // 尝试获取Canvas节点
      const query = wx.createSelectorQuery();
      query.select('#processCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            webglContext.canvasNode = res[0].node;
            console.log('成功获取Canvas节点');
            // 重新调用保存函数
            that._saveCanvasToAlbum();
          } else {
            wx.showToast({
              title: '无法获取画布节点',
              icon: 'none',
              duration: 1500
            });
          }
        });
      return;
    }
    
    // 添加调试提示
    console.log('开始保存爱宠视角图片', 
                '当前视图:', that.data.currentView,
                'WebGL已初始化:', !!webglContext.gl,
                'Canvas尺寸:', webglContext.canvasNode.width, 'x', webglContext.canvasNode.height);
    
    // 1. 检查相册写入权限
    wx.getSetting({
      success(res) {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已经授权，直接保存
          that._saveCanvasToAlbum();
        } else {
          // 未授权或曾拒绝授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              // 用户同意授权
              that._saveCanvasToAlbum();
            },
            fail() {
              // 用户拒绝授权
              wx.showModal({
                title: '授权提示',
                content: '需要您授权保存图片到相册才能使用该功能，是否前往设置页面授权？',
                success(modalRes) {
                  if (modalRes.confirm) {
                    // 用户确认，打开设置页面
                    wx.openSetting({
                      success(settingRes) {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          // 用户在设置页面授权了
                          that._saveCanvasToAlbum();
                        } else {
                          // 用户在设置页面未授权
                          wx.showToast({
                            title: '授权失败，无法保存',
                            icon: 'none'
                          });
                        }
                      }
                    });
                  } else {
                    // 用户取消
                    wx.showToast({
                      title: '授权失败，无法保存',
                      icon: 'none'
                    });
                  }
                }
              });
            }
          });
        }
      }
    });
  },
  
  // 内部方法：将 Canvas 保存到相册
  _saveCanvasToAlbum: function() {
    const that = this;
    
    // 确保授权重试计数器已初始化
    if (typeof this._authRetryCount === 'undefined') {
      this._authRetryCount = 0;
    }
    
    wx.showLoading({ title: '正在保存...', mask: true }); // 显示加载提示
    
    try {
      // 确保WebGL上下文和Canvas可用
      if (!webglContext || !webglContext.gl) {
        wx.hideLoading();
        wx.showToast({
          title: 'WebGL上下文不可用',
          icon: 'none'
        });
        return;
      }
      
      // 确保Canvas节点存在
      if (!webglContext.canvasNode) {
        console.log('尝试获取Canvas节点');
        const query = wx.createSelectorQuery();
        query.select('#processCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res && res[0] && res[0].node) {
              webglContext.canvasNode = res[0].node;
              console.log('成功获取Canvas节点，继续保存过程');
              // 重新调用保存函数
              that._saveCanvasToAlbum();
            } else {
              wx.hideLoading();
              wx.showToast({
                title: '无法获取画布节点',
                icon: 'none'
              });
            }
          });
        return;
      }
      
      const gl = webglContext.gl;
      const canvas = webglContext.canvasNode;
      const width = canvas.width;
      const height = canvas.height;
      
      // 获取当前帧
      let frameData = null;
      if (this._lastFrame) {
        // 如果有缓存的原始帧，使用它重新渲染一次确保内容是最新的
        console.log('使用已缓存的原始帧重新渲染一次');
        this.processFrameWebGL(this._lastFrame);
        
        // 强制执行一次渲染刷新
        gl.finish();
      }
      
      // 临时禁用深度测试和模板测试，确保能读取所有像素
      const depthTest = gl.getParameter(gl.DEPTH_TEST);
      if (depthTest) {
        gl.disable(gl.DEPTH_TEST);
      }
      
      // 创建一个临时2D画布用于保存图片
      const tmpCanvas = wx.createOffscreenCanvas({
        type: '2d',
        width: width,
        height: height
      });
      const ctx = tmpCanvas.getContext('2d');
      
      // 从 WebGL 读取像素数据
      const pixels = new Uint8Array(width * height * 4);
      
      // 确保使用RGBA格式读取所有像素
      gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);
      
      // 输出调试信息，检查像素数据是否有效
      let nonZeroPixels = 0;
      for (let i = 0; i < pixels.length; i += 4) {
        if (pixels[i] > 0 || pixels[i+1] > 0 || pixels[i+2] > 0) {
          nonZeroPixels++;
        }
      }
      console.log(`读取的像素数据: 总像素: ${width * height}, 非黑色像素: ${nonZeroPixels}`);
      
      // 如果没有非黑色像素，尝试强制渲染
      if (nonZeroPixels === 0 && webglContext.texture) {
        console.log('尝试强制渲染并再次读取像素');
        
        // 强制渲染一个全屏四边形
        gl.viewport(0, 0, width, height);
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        
        // 使用着色器程序
        if (webglContext.program) {
          gl.useProgram(webglContext.program);
          
          // 设置顶点
          if (webglContext.buffers && webglContext.buffers.position) {
            gl.bindBuffer(gl.ARRAY_BUFFER, webglContext.buffers.position);
            gl.vertexAttribPointer(
              webglContext.programInfo.attribLocations.vertexPosition,
              2, gl.FLOAT, false, 0, 0
            );
            gl.enableVertexAttribArray(webglContext.programInfo.attribLocations.vertexPosition);
            
            // 设置纹理坐标
            gl.bindBuffer(gl.ARRAY_BUFFER, webglContext.buffers.textureCoord);
            gl.vertexAttribPointer(
              webglContext.programInfo.attribLocations.textureCoord,
              2, gl.FLOAT, false, 0, 0
            );
            gl.enableVertexAttribArray(webglContext.programInfo.attribLocations.textureCoord);
            
            // 绑定纹理
            gl.activeTexture(gl.TEXTURE0);
            gl.bindTexture(gl.TEXTURE_2D, webglContext.texture);
            gl.uniform1i(webglContext.programInfo.uniformLocations.sampler, 0);
            
            // 绘制
            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
            gl.finish();
            
            // 再次读取像素
            gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);
            
            // 再次检查像素数据
            nonZeroPixels = 0;
            for (let i = 0; i < pixels.length; i += 4) {
              if (pixels[i] > 0 || pixels[i+1] > 0 || pixels[i+2] > 0) {
                nonZeroPixels++;
              }
            }
            console.log(`第二次读取的像素数据: 总像素: ${width * height}, 非黑色像素: ${nonZeroPixels}`);
          }
        }
        
        // 如果依然全黑，需要创建一个测试渐变图像
        if (nonZeroPixels === 0) {
          console.warn('WebGL渲染后依然无法获取有效像素，创建测试图像');
          
          // 创建一个颜色渐变图，确认保存流程正常
          for (let i = 0; i < pixels.length; i += 4) {
            // 创建渐变色
            const row = Math.floor((i/4) / width);
            const col = (i/4) % width;
            
            // 渐变色方案
            pixels[i] = Math.floor(255 * (col / width));         // R从左到右渐变
            pixels[i+1] = Math.floor(255 * (row / height));      // G从上到下渐变
            pixels[i+2] = Math.floor(255 * (1 - col / width));   // B从右到左渐变
            pixels[i+3] = 255;                                   // A固定不透明
          }
          
          // 添加白色文字指示这是测试图像
          const centerX = Math.floor(width / 2);
          const centerY = Math.floor(height / 2);
          const textWidth = Math.floor(width * 0.7);
          const textHeight = Math.floor(height * 0.1);
          
          // 在中心绘制白色矩形
          for (let y = centerY - textHeight; y < centerY + textHeight; y++) {
            for (let x = centerX - textWidth/2; x < centerX + textWidth/2; x++) {
              if (x >= 0 && x < width && y >= 0 && y < height) {
                const i = (y * width + x) * 4;
                pixels[i] = 255;    // R
                pixels[i+1] = 255;  // G
                pixels[i+2] = 255;  // B
                pixels[i+3] = 255;  // A
              }
            }
          }
          
          console.log('已创建测试图像');
        }
      }
      
      // 创建ImageData并上下翻转(WebGL坐标系Y轴是从下到上)
      const imageData = ctx.createImageData(width, height);
      const data = imageData.data;
      
      for (let row = 0; row < height; row++) {
        for (let col = 0; col < width; col++) {
          // 翻转Y坐标，因为WebGL和Canvas 2D的坐标系不同
          const sourceIndex = (col + (height - row - 1) * width) * 4;
          const targetIndex = (col + row * width) * 4;
          
          data[targetIndex] = pixels[sourceIndex];         // R
          data[targetIndex + 1] = pixels[sourceIndex + 1]; // G
          data[targetIndex + 2] = pixels[sourceIndex + 2]; // B
          data[targetIndex + 3] = 255; // 设置为完全不透明
        }
      }
      
      // 将ImageData绘制到临时画布
      ctx.putImageData(imageData, 0, 0);
      
      // 恢复WebGL状态
      if (depthTest) {
        gl.enable(gl.DEPTH_TEST);
      }
      
      // 将临时画布转换为临时文件
      const tempFilePath = tmpCanvas.toDataURL('image/png');
      
      // 将Data URL转换为临时文件
      const fs = wx.getFileSystemManager();
      const tempFileName = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.png`;
      
      // 写入文件系统
      fs.writeFile({
        filePath: tempFileName,
        data: tempFilePath.replace(/^data:image\/\w+;base64,/, ''),
        encoding: 'base64',
        success() {
          console.log('临时文件创建成功:', tempFileName);
          
          // 保存到相册
          wx.saveImageToPhotosAlbum({
            filePath: tempFileName,
            success() {
              // 保存成功，重置授权重试计数器
              that._authRetryCount = 0;
              
              // 删除临时文件
              fs.unlink({
                filePath: tempFileName,
                complete() {
                  wx.hideLoading();
                  wx.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                }
              });
            },
            fail(err) {
              console.error('保存图片到相册失败:', err);
              
              // 判断是否为授权失败错误
              if (err.errMsg && (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize') >= 0)) {
                // 授权失败，尝试重新授权
                if (that._authRetryCount < 1) {
                  that._authRetryCount++;
                  console.log('授权失败，尝试重新授权，当前重试次数:', that._authRetryCount);
                  
                  // 延迟后重试，先删除临时文件
                  fs.unlink({
                    filePath: tempFileName,
                    complete() {
                      // 显示重新授权对话框
                      wx.hideLoading();
                      wx.showModal({
                        title: '授权提示',
                        content: '保存图片需要授权访问您的相册，是否重新授权？',
                        success(modalRes) {
                          if (modalRes.confirm) {
                            wx.openSetting({
                              success(settingRes) {
                                if (settingRes.authSetting['scope.writePhotosAlbum']) {
                                  // 用户在设置页面授权了，重新尝试保存
                                  setTimeout(() => {
                                    that.savePetVisionImage();
                                  }, 500);
                                } else {
                                  wx.showToast({
                                    title: '保存失败，请授权相册访问权限',
                                    icon: 'none'
                                  });
                                }
                              }
                            });
                          } else {
                            wx.showToast({
                              title: '保存失败，已取消授权',
                              icon: 'none'
                            });
                          }
                        }
                      });
                    }
                  });
                } else {
                  // 超过重试次数，直接提示失败
                  fs.unlink({
                    filePath: tempFileName,
                    complete() {
                      wx.hideLoading();
                      wx.showToast({
                        title: '保存失败，请检查相册权限',
                        icon: 'none'
                      });
                    }
                  });
                }
              } else {
                // 其他类型的错误，提示失败
                fs.unlink({
                  filePath: tempFileName,
                  complete() {
                    wx.hideLoading();
                    wx.showToast({
                      title: '保存失败，请重试',
                      icon: 'none'
                    });
                  }
                });
              }
            }
          });
        },
        fail(err) {
          wx.hideLoading();
          console.error('创建临时文件失败:', err);
          wx.showToast({
            title: '创建临时文件失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('保存图片过程中出错:', error);
      wx.showToast({
        title: '保存图片出错',
        icon: 'none'
      });
    }
  },

  // 分享小程序
  // 禁用分享功能
  onShareAppMessage: function() {
    return {
      title: '爱宠视觉',
      path: '/pages/index/index',
      imageUrl: '/images/share-image.png',
      success: function(res) {
        console.log('分享成功');
      },
      fail: function(res) {
        console.log('分享失败');
      }
    };
  },

  // 检查是否需要显示视觉功能层级引导
  checkShowVisionGuide: function() {
    // 从本地存储中读取用户设置
    const hideVisionGuide = wx.getStorageSync('hideVisionGuide');
    
    // 如果用户未设置隐藏引导，则显示
    if (!hideVisionGuide) {
      // 延迟1秒显示引导，让页面先完成加载
      setTimeout(() => {
        this.setData({
          showVisionGuide: true
        });
      }, 1000);
    }
  },

  // 关闭视觉功能层级引导
  closeVisionGuide: function() {
    this.setData({
      showVisionGuide: false
    });
    
    // 如果用户选择了"不再显示"，则记录到本地存储
    if (this.data.rememberVisionGuide) {
      wx.setStorage({
        key: 'hideVisionGuide',
        data: true
      });
    }
  },

  // 切换"不再显示"选项
  toggleRememberGuide: function() {
    this.setData({
      rememberVisionGuide: !this.data.rememberVisionGuide
    });
  },

  // 防止冒泡
  preventBubble: function(e) {
    return false;
  },

  // 显示运动视觉引导提示
  showMotionVisionGuideTip: function() {
    this.setData({
      showMotionGuideTip: true
    });
    
    // 5秒后自动隐藏提示
    setTimeout(() => {
      this.setData({
        showMotionGuideTip: false
      });
    }, 5000);
  },

  // 注册主题变化回调
  registerThemeCallback: function() {
    // 先移除可能存在的旧回调，避免重复
    this.unregisterThemeCallback();
    
    // 添加新的回调
    if (app.themeChangeCallbacks) {
      app.themeChangeCallbacks.push(this.updateTheme.bind(this));
      console.log('视觉模拟页面：已注册主题变化回调');
    }
  },

  // 移除主题变化回调
  unregisterThemeCallback: function() {
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback => 
        callback.toString() === this.updateTheme.bind(this).toString());
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
        console.log('视觉模拟页面：已移除主题变化回调');
      }
    }
  },

  onShow: function() {
    // 重新注册主题回调
    this.registerThemeCallback();

    // 从全局获取当前主题并应用
    const currentTheme = app.globalData.currentTheme || 'theme1';
    if (this.data && this.data.currentTheme !== currentTheme) {
      console.log('视觉模拟页面显示：检测到主题变化，应用新主题');
      this.updateTheme();
    }

    // 使用生命周期管理器处理页面显示
    lifecycleManager.onShow(this);

    // 处理相机状态
    if (this.data.cameraLoading) {
      console.log('页面显示时检测到相机处于加载状态，可能需要重新初始化');
      // 设置一个计时器，如果相机加载状态持续太久，尝试重新初始化
      if (this._cameraLoadingTimer) {
        clearTimeout(this._cameraLoadingTimer);
      }
      
      this._cameraLoadingTimer = setTimeout(() => {
        if (this.data && this.data.cameraLoading) {
          console.log('相机加载状态持续超过预期，尝试重新初始化');
          this.retryCameraInit();
        }
      }, 2500); // 如果2.5秒后仍然在加载，则重试
    } 
    // 检查最后一帧的时间
    else if (this._lastFrameCheckTime) {
      const now = Date.now();
      // 如果超过3秒没有收到新帧，认为相机可能已经停止工作
      if (now - this._lastFrameCheckTime > 3000) {
        console.log('页面显示时检测到相机长时间未收到新帧，尝试重新初始化');
        this.retryCameraInit();
      }
    }
  },

  // 重置视觉参数
  resetVisionParams: function() {
    // 获取当前品种名称
    const breedName = this.data.breedName;
    const isCat = this.data.features.isCat;
    
    // 创建重置参数对象
    let resetParams = { ...this.data.originalDogVisionParams };
    
    // 根据品种调整特定参数
    if (isCat) {
      // 猫科动物处理
      if (breedName === '暹罗') {
        resetParams.resolutionFactor = 0.15; // 暹罗猫特殊视力清晰度因子
        resetParams.brightness = 2.0; // 暹罗猫夜视能力参数
        resetParams.contrast = 1.5;
      } else {
        resetParams.resolutionFactor = 0.2; // 其他猫科视力清晰度因子
      }
    } else {
      // 犬科动物处理
      if (breedName.includes('哈士奇')) {
        resetParams.brightness = 1.8; // 哈士奇夜视能力更强
        resetParams.resolutionFactor = 0.27; // 哈士奇视力约为人类的4/15
      } else if (breedName.includes('边境') || breedName.includes('牧羊')) {
        resetParams.resolutionFactor = 0.4; // 边牧视力约为人类的2/5
      } else if (breedName.includes('金毛')) {
        resetParams.resolutionFactor = 0.33; // 金毛视力约为人类的1/3
      } else if (breedName.includes('柯基')) {
        resetParams.resolutionFactor = 0.27; // 柯基视力约为人类的4/15
      } else if (breedName.includes('贵宾') || breedName.includes('泰迪')) {
        resetParams.resolutionFactor = 0.27; // 泰迪视力约为人类的4/15
        resetParams.brightness = 1.3; // 泰迪夜视能力较弱
      } else if (breedName.includes('拉布拉多')) {
        resetParams.resolutionFactor = 1.0; // 拉布拉多视力接近于人类
        resetParams.brightness = 1.6; // 拉布拉多夜视能力较强
      } else if (breedName.includes('吉娃娃') || breedName.includes('法国') || breedName.includes('法斗')) {
        resetParams.resolutionFactor = 0.25; // 约为人类的1/4
      } else if (breedName.includes('比熊')) {
        resetParams.resolutionFactor = 0.27; // 比熊视力约为人类的4/15
      }
    }
    
    // 更新UI和视觉管理器
    this.setData({
      dogVisionParams: resetParams
    });
    
    console.log('重置品种[' + breedName + ']的视觉参数:', resetParams);
    
    // 更新视觉管理器配置
    if (visionManager) {
      // 更新视力辨析度配置
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: resetParams.resolutionFactor,
        VIEW_FIELD_FACTOR: resetParams.antiAliasFactor
      });
      
      // 更新夜视能力配置
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: resetParams.brightness,
        CONTRAST: resetParams.contrast
      });
      
      // 如果处于运动视觉模式，更新运动视觉配置
      if (this.data.features.motion) {
        visionManager.updateMotionVisionConfig({
          MOTION_SENSITIVITY: resetParams.motionSensitivity,
          MOTION_THRESHOLD: resetParams.motionThreshold,
          MOTION_SIZE_THRESHOLD: resetParams.motionSizeThreshold
        });
      }
      
      // 如果有最后一帧，重新渲染
      if (this._lastFrame && webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
    
    // 添加触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  // ==================== CAM硬件相机相关方法 ====================
  
  // 初始化CAM管理器
  initCamManager: function() {
    console.log('初始化CAM管理器');
    
    camManager.initCamManager(this)
      .then(() => {
        console.log('CAM管理器初始化成功');
        
        // 设置错误回调
        camManager.setCamErrorCallback((errorMsg) => {
          console.error('CAM错误回调:', errorMsg);
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          });
        });
      })
      .catch((error) => {
        console.error('CAM管理器初始化失败:', error);
      });
  },
  
  // toggleCamMode函数已被新的相机选择器系统取代
  // 保留方法名称以兼容可能的外部调用
  toggleCamMode: function() {
    console.log('toggleCamMode已被新的相机选择器取代，请使用相机选择器进行切换');
    // 打开相机选择器
    this.toggleCameraSelector();
  },
  
  // 启用CAM模式
  enableCamMode: function() {
    console.log('启用CAM模式，开始释放手机相机资源');
    
    // === 1. 停止并释放手机相机相关资源 ===
    
    // 停止相机上下文和帧监听器
    if (this.cameraCtx) {
      try {
        console.log('停止手机相机上下文');
        cameraManager.stopCamera(this.cameraCtx);
        this.cameraCtx = null;
        cameraCtx = null; // 清除全局变量
      } catch (err) {
        console.error('停止手机相机失败:', err);
      }
    }
    
    // 停止FPS计数器
    if (fpsUpdateInterval) {
      clearInterval(fpsUpdateInterval);
      fpsUpdateInterval = null;
      console.log('停止FPS计数器');
    }
    
    // 清除所有相机相关定时器
    if (this._cameraLoadingTimer) {
      clearTimeout(this._cameraLoadingTimer);
      this._cameraLoadingTimer = null;
      console.log('清除相机加载定时器');
    }
    
    if (this._retryTimer) {
      clearTimeout(this._retryTimer);
      this._retryTimer = null;
      console.log('清除重试定时器');
    }
    
    // 清除所有安全定时器
    if (this._safetyTimers && this._safetyTimers.length > 0) {
      this._safetyTimers.forEach(timer => {
        clearTimeout(timer);
      });
      this._safetyTimers = [];
      console.log('清除所有安全定时器');
    }
    
    // 重置相机相关状态标记
    this._cameraInitialized = false;
    this._lastFrameCheckTime = null;
    this._lastFrame = null;
    
    // === 2. 临时清理WebGL资源（但保留上下文） ===
    
    if (webglContext && webglContext.gl) {
      try {
        console.log('临时清理WebGL纹理资源');
        const gl = webglContext.gl;
        
        // 清除当前绑定的纹理
        gl.bindTexture(gl.TEXTURE_2D, null);
        
        // 清除帧数据引用
        webglContext.rawFrame = null;
        webglContext.firstFrame = true; // 重置第一帧标记
        
        // 清空画布
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        
        console.log('WebGL纹理资源已临时清理');
      } catch (err) {
        console.error('清理WebGL纹理资源失败:', err);
      }
    }
    
    // === 3. 清理图片缓存和数据 ===
    
    // 清除页面上可能存在的图片缓存
    this.setData({
      // 清除手机相机相关的图片缓存
      cameraLoading: false,
      cameraError: false,
      cameraErrorMsg: '',
      cameraInitializing: false,
      showCameraSettingBtn: false,
      // 重置FPS显示
      currentFPS: 0
    });
    
    // === 4. 强制内存清理 ===
    
    this._forceMemoryCleanup();
    
    console.log('手机相机资源释放完成');
    
    // === 5. 设置CAM模式状态 ===
    
    // 设置CAM模式状态
    this.setData({
      camMode: true,
      camIsLoading: true,
      camStatus: 'connecting',
      currentCameraType: 'cam' // 同步更新相机类型
    });
    
    console.log('CAM模式状态设置完成');
    
    // === 6. 启动CAM硬件相机 ===
    
    // 添加小延迟再连接，让状态切换更稳定
    setTimeout(() => {
      // 连接CAM硬件相机
      camManager.connectCam()
        .then(() => {
          console.log('CAM硬件相机连接成功，已切换到CAM模式');
          wx.showToast({
            title: '硬件相机连接成功',
            icon: 'success',
            duration: 1500
          });
        })
        .catch((error) => {
          console.error('CAM硬件相机连接失败:', error);
          
          // 连接失败，自动退回手机摄像头模式
          this.disableCamMode();
          
          wx.showToast({
            title: '硬件相机连接失败',
            icon: 'none',
            duration: 2000
          });
        });
    }, 600); // 增加延迟，确保资源完全释放
  },
  
  // 禁用CAM模式
  disableCamMode: function() {
    console.log('禁用CAM模式，准备恢复手机相机');
    
    // === 1. 断开CAM连接和资源 ===
    
    // 断开CAM连接
    camManager.disconnectCam();
    
    // 清理CAM相关的图片缓存
    this.setData({
      camMode: false,
      camStatus: 'disconnected',
      camImageUrl: '',
      camNextImageUrl: '',
      camIsLoading: false,
      camErrorMsg: '',
      currentCameraType: this.data.cameraPosition // 恢复到手机相机类型
    });
    
    // === 2. 清理CAM图片内存 ===
    
    // 强制清理图片缓存
    this._forceCamImageCleanup();
    
    console.log('CAM资源已释放');
    
    // === 3. 恢复WebGL资源 ===
    
    if (webglContext && webglContext.gl) {
      try {
        console.log('恢复WebGL资源');
        const gl = webglContext.gl;
        
        // 重新绑定纹理（如果存在）
        if (webglContext.texture) {
          gl.bindTexture(gl.TEXTURE_2D, webglContext.texture);
          // 重新初始化空白纹理
          gl.texImage2D(
            gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE,
            new Uint8Array([0, 0, 0, 255])
          );
        }
        
        // 重置第一帧标记
        webglContext.firstFrame = true;
        
        console.log('WebGL资源已恢复');
      } catch (err) {
        console.error('恢复WebGL资源失败:', err);
      }
    }
    
    // === 4. 恢复手机相机 ===
    
    // 如果页面未隐藏，重新初始化手机相机和相关资源
    if (!this._pageHidden) {
      console.log('准备恢复手机相机模式');
      
      // 重新启动FPS计数器
      this.startFPSCounter();
      
      // 重新初始化手机相机
      setTimeout(() => {
        if (!this._pageHidden && !this.data.camMode) { // 确保还在手机相机模式
          console.log('开始重新初始化手机相机');
          this.initCamera();
        }
      }, 1000); // 给CAM资源更多时间完全释放
    } else {
      console.log('页面已隐藏，暂不恢复手机相机');
    }
    
    wx.showToast({
      title: '已切换回手机相机',
      icon: 'none',
      duration: 1500
    });
  },
  
  // 重试CAM连接
  retryCamConnection: function() {
    console.log('重试CAM连接');
    
    if (this.data.camMode) {
      // 先断开现有连接
      camManager.disconnectCam();
      
      // 延迟后重新连接
      setTimeout(() => {
        this.enableCamMode();
      }, 1000);
    }
  },
  
  // 处理CAM图片加载错误
  handleCamImageError: function(e) {
    console.error('CAM图片加载错误:', e);
    
    // 获取是哪个缓冲区的图片加载失败
    const bufferType = e.currentTarget.dataset.buffer;
    console.log('CAM图片加载失败，缓冲区:', bufferType);
    
    // 调用CAM管理器的错误处理方法
    camManager.handleImageLoadError();
  },
  
  // 处理CAM图片加载完成
  handleCamImageLoad: function(e) {
    console.log('CAM图片加载成功:', e.detail);
    
    // 获取是哪个缓冲区的图片加载成功
    const bufferType = e.currentTarget.dataset.buffer;
    const imageWidth = e.detail.width || 0;
    const imageHeight = e.detail.height || 0;
    
    console.log('CAM图片详细信息:', {
      width: imageWidth,
      height: imageHeight,
      src: e.detail.src,
      bufferType: bufferType,
      currentBuffer: this.data.camCurrentBuffer
    });
    
    // 验证图像尺寸
    if (imageWidth === 0 || imageHeight === 0) {
      console.warn('CAM图像尺寸异常:', { width: imageWidth, height: imageHeight });
      // 尺寸异常时当作加载错误处理
      camManager.handleImageLoadError();
      return;
    }
    
    // 调用CAM管理器的成功处理方法
    camManager.handleImageLoadSuccess(bufferType);
  },

  // ==================== 内存管理辅助函数 ====================
  
  // 强制内存清理
  _forceMemoryCleanup: function() {
    try {
      console.log('执行强制内存清理');
      
      // 清理大对象引用
      this._tempImageData = null;
      this._tempCanvasData = null;
      
      // 如果有缓存的图片URL，释放它们
      if (this._cachedImageUrls) {
        this._cachedImageUrls.forEach(url => {
          if (url && typeof url === 'string' && url.startsWith('blob:')) {
            try {
              URL.revokeObjectURL(url);
            } catch (e) {
              // 忽略撤销错误
            }
          }
        });
        this._cachedImageUrls = [];
      }
      
      // 尝试触发垃圾回收（仅在调试环境有效）
      if (typeof wx.triggerGC === 'function') {
        wx.triggerGC();
        console.log('已触发垃圾回收');
      }
      
      console.log('内存清理完成');
    } catch (error) {
      console.error('内存清理失败:', error);
    }
  },
  
  // 强制清理CAM图片缓存
  _forceCamImageCleanup: function() {
    try {
      console.log('清理CAM图片缓存');
      
      // 清理可能的图片URL引用
      const currentUrls = [
        this.data.camImageUrl,
        this.data.camNextImageUrl
      ];
      
      currentUrls.forEach(url => {
        if (url && typeof url === 'string') {
          // 如果是blob URL，撤销它
          if (url.startsWith('blob:')) {
            try {
              URL.revokeObjectURL(url);
              console.log('已撤销blob URL:', url.substring(0, 50) + '...');
            } catch (e) {
              // 忽略撤销错误
            }
          }
        }
      });
      
      // 清理临时图片数据
      this._tempCamImageData = null;
      
      console.log('CAM图片缓存清理完成');
    } catch (error) {
      console.error('CAM图片缓存清理失败:', error);
    }
  },
  
  // 监控内存使用情况
  _monitorMemoryUsage: function() {
    try {
      if (typeof wx.getSystemInfo === 'function') {
        wx.getSystemInfo({
          success: (res) => {
            const memoryWarningLevel = res.memoryWarningLevel;
            if (memoryWarningLevel && memoryWarningLevel > 0) {
              console.warn('内存警告级别:', memoryWarningLevel);
              // 如果内存警告，执行清理
              this._forceMemoryCleanup();
            }
          },
          fail: (err) => {
            console.log('获取系统信息失败:', err);
          }
        });
      }
    } catch (error) {
      console.error('内存监控失败:', error);
    }
  },

});
