/**
 * 选择器相关样式模块
 * 包含相机选择器、分辨率选择器、视觉模式选择器等样式
 */

/* 选择器遮罩层通用样式 */
.selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.selector-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 选择器面板通用样式 */
.selector-panel {
  width: 80%;
  max-width: 600rpx;
  background: rgba(40, 60, 90, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: scale(0.9) translateY(50rpx);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-overlay.show .selector-panel {
  transform: scale(1) translateY(0);
}

/* 浅色主题下的面板样式 */
.light-theme-content .selector-panel {
  background: rgba(250, 250, 250, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 选择器头部 */
.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.light-theme-content .selector-header {
  border-bottom-color: rgba(0, 0, 0, 0.1);
}

.selector-title {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.light-theme-content .selector-title {
  color: rgba(0, 0, 0, 0.9);
}

.selector-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.light-theme-content .selector-close {
  color: rgba(0, 0, 0, 0.6);
  background: rgba(0, 0, 0, 0.05);
}

.selector-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

.light-theme-content .selector-close:active {
  background: rgba(0, 0, 0, 0.1);
}

/* 选择器内容区域 */
.selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 40rpx;
}

/* 选择器选项通用样式 */
.selector-option {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.light-theme-content .selector-option {
  background: rgba(0, 0, 0, 0.05);
}

.selector-option:last-child {
  margin-bottom: 0;
}

.selector-option:active {
  transform: scale(0.98);
}

.selector-option.active {
  background: rgba(255, 138, 91, 0.15);
  border-color: rgba(255, 138, 91, 0.3);
  box-shadow: 0 4px 16px rgba(255, 138, 91, 0.2);
}

.light-theme-content .selector-option.active {
  background: rgba(255, 138, 91, 0.1);
  border-color: rgba(255, 138, 91, 0.3);
}

.selector-option.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 选项图标 */
.option-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.1);
}

.light-theme-content .option-icon {
  background: rgba(0, 0, 0, 0.05);
}

.selector-option.active .option-icon {
  background: rgba(255, 138, 91, 0.2);
}

/* 选项内容 */
.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.option-name {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

.light-theme-content .option-name {
  color: rgba(0, 0, 0, 0.9);
}

.option-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

.light-theme-content .option-desc {
  color: rgba(0, 0, 0, 0.6);
}

.option-note {
  font-size: 22rpx;
  color: rgba(255, 193, 7, 0.8);
  margin-top: 8rpx;
  font-style: italic;
}

.light-theme-content .option-note {
  color: rgba(255, 152, 0, 0.8);
}

/* 选项状态 */
.option-status {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.status-selected {
  color: rgba(50, 205, 50, 0.9);
  font-size: 32rpx;
  font-weight: bold;
}

.status-connected {
  color: rgba(50, 205, 50, 0.9);
  font-size: 24rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 选择器底部 */
.selector-footer {
  padding: 20rpx 40rpx 40rpx;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.light-theme-content .selector-footer {
  border-top-color: rgba(0, 0, 0, 0.1);
}

.selector-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.4;
}

.light-theme-content .selector-tip {
  color: rgba(0, 0, 0, 0.5);
}

/* 视觉模式选择器特殊样式 */
.vision-mode-selector {
  /* 继承通用选择器样式 */
}

.vision-mode-option {
  /* 继承通用选项样式 */
  flex-direction: column;
  align-items: flex-start;
  padding: 40rpx 30rpx;
}

.vision-mode-header {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 20rpx;
}

.vision-mode-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.1);
}

.vision-mode-info {
  flex: 1;
}

.vision-mode-name {
  font-size: 30rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 6rpx;
}

.light-theme-content .vision-mode-name {
  color: rgba(0, 0, 0, 0.9);
}

.vision-mode-level {
  font-size: 22rpx;
  color: rgba(255, 138, 91, 0.8);
  font-weight: 500;
}

.vision-mode-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.light-theme-content .vision-mode-desc {
  color: rgba(0, 0, 0, 0.7);
}

.vision-mode-includes {
  font-size: 22rpx;
  color: rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .selector-panel {
    width: 90%;
    max-width: none;
  }
  
  .selector-header {
    padding: 30rpx 30rpx 16rpx;
  }
  
  .selector-title {
    font-size: 32rpx;
  }
  
  .selector-close {
    width: 50rpx;
    height: 50rpx;
    font-size: 36rpx;
  }
  
  .selector-content {
    padding: 16rpx 30rpx;
  }
  
  .selector-option {
    padding: 24rpx 16rpx;
  }
  
  .option-icon {
    width: 64rpx;
    height: 64rpx;
    font-size: 40rpx;
    margin-right: 20rpx;
  }
  
  .option-name {
    font-size: 28rpx;
  }
  
  .option-desc {
    font-size: 22rpx;
  }
  
  .vision-mode-option {
    padding: 30rpx 20rpx;
  }
  
  .vision-mode-icon {
    width: 50rpx;
    height: 50rpx;
    font-size: 32rpx;
  }
  
  .vision-mode-name {
    font-size: 26rpx;
  }
  
  .vision-mode-desc {
    font-size: 22rpx;
  }
  
  .vision-mode-includes {
    font-size: 20rpx;
  }
}
