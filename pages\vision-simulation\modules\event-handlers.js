/**
 * 事件处理器模块
 * 负责处理页面的各种用户交互事件
 * 从主文件中提取事件处理相关的逻辑，提高代码可维护性
 */

// 导入相关模块
const uiController = require('../utils/ui-controller');
const pageDataManager = require('./page-data-manager');

/**
 * 切换标签页
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function changeTab(page, event) {
  const index = parseInt(event.currentTarget.dataset.index);
  uiController.changeTab(index, page);
}

/**
 * 滑动切换标签页
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function swiperChange(page, event) {
  uiController.swiperChange(event, page);
}

/**
 * 切换视角（人类/宠物）
 * @param {Object} page - 页面实例
 */
function toggleView(page) {
  uiController.toggleView(page);
}

/**
 * 切换相机位置（前置/后置）
 * @param {Object} page - 页面实例
 */
function switchCameraPosition(page) {
  uiController.switchCameraPosition(page);
}

/**
 * 显示/隐藏视觉参数调整面板
 * @param {Object} page - 页面实例
 */
function toggleVisionParams(page) {
  uiController.toggleVisionParams(page);
}

/**
 * 重置视觉参数到默认值
 * @param {Object} page - 页面实例
 */
function resetVisionParams(page) {
  uiController.resetVisionParams(page);
}

/**
 * 亮度滑块变化处理
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function onBrightnessChange(page, event) {
  uiController.onBrightnessChange(event, page);
}

/**
 * 对比度滑块变化处理
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function onContrastChange(page, event) {
  uiController.onContrastChange(event, page);
}

/**
 * 辨析度滑块变化处理
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function onResolutionChange(page, event) {
  uiController.onResolutionChange(event, page);
}

/**
 * 抗锯齿强度滑块变化处理
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function onAntiAliasChange(page, event) {
  uiController.onAntiAliasChange(event, page);
}

/**
 * 展开/收起分析区域
 * @param {Object} page - 页面实例
 */
function toggleAnalysisExpanded(page) {
  const isExpanded = !page.data.isAnalysisExpanded;
  page.setData({
    isAnalysisExpanded: isExpanded
  });
  
  console.log('分析区域展开状态:', isExpanded);
}

/**
 * 显示/隐藏控制面板
 * @param {Object} page - 页面实例
 */
function toggleControls(page) {
  const showControls = !page.data.showControls;
  page.setData({
    showControls: showControls
  });
  
  console.log('控制面板显示状态:', showControls);
}

/**
 * 显示相机选择器
 * @param {Object} page - 页面实例
 */
function showCameraSelector(page) {
  page.setData({
    showCameraSelector: true
  });
}

/**
 * 隐藏相机选择器
 * @param {Object} page - 页面实例
 */
function hideCameraSelector(page) {
  page.setData({
    showCameraSelector: false
  });
}

/**
 * 选择相机类型
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function selectCameraType(page, event) {
  const cameraType = event.currentTarget.dataset.type;
  
  if (cameraType === page.data.currentCameraType) {
    // 如果选择的是当前相机类型，直接关闭选择器
    hideCameraSelector(page);
    return;
  }
  
  console.log('切换相机类型:', cameraType);
  
  page.setData({
    currentCameraType: cameraType,
    showCameraSelector: false,
    cameraLoading: true
  });
  
  // 根据相机类型执行相应的初始化
  if (cameraType === 'cam') {
    // 初始化硬件相机
    if (typeof page.initCAMCamera === 'function') {
      page.initCAMCamera();
    }
  } else {
    // 初始化手机相机
    if (typeof page.initCamera === 'function') {
      page.setData({ cameraPosition: cameraType });
      page.initCamera();
    }
  }
}

/**
 * 显示分辨率选择器
 * @param {Object} page - 页面实例
 */
function showResolutionSelector(page) {
  page.setData({
    showResolutionSelector: true
  });
}

/**
 * 隐藏分辨率选择器
 * @param {Object} page - 页面实例
 */
function hideResolutionSelector(page) {
  page.setData({
    showResolutionSelector: false
  });
}

/**
 * 选择分辨率
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function selectResolution(page, event) {
  const index = parseInt(event.currentTarget.dataset.index);
  
  if (index === page.data.currentResolutionIndex) {
    // 如果选择的是当前分辨率，直接关闭选择器
    hideResolutionSelector(page);
    return;
  }
  
  console.log('切换分辨率:', page.data.resolutionOptions[index]);
  
  page.setData({
    currentResolutionIndex: index,
    showResolutionSelector: false,
    cameraLoading: true
  });
  
  // 重新初始化相机以应用新分辨率
  if (typeof page.initCamera === 'function') {
    page.initCamera();
  }
}

/**
 * 显示视觉模式选择器
 * @param {Object} page - 页面实例
 */
function showVisionModeSelector(page) {
  page.setData({
    showVisionModeSelector: true
  });
}

/**
 * 隐藏视觉模式选择器
 * @param {Object} page - 页面实例
 */
function hideVisionModeSelector(page) {
  page.setData({
    showVisionModeSelector: false
  });
}

/**
 * 选择视觉模式
 * @param {Object} page - 页面实例
 * @param {Object} event - 事件对象
 */
function selectVisionMode(page, event) {
  const modeId = event.currentTarget.dataset.mode;
  
  if (modeId === page.data.currentVisionMode) {
    // 如果选择的是当前模式，直接关闭选择器
    hideVisionModeSelector(page);
    return;
  }
  
  console.log('切换视觉模式:', modeId);
  
  // 根据模式设置功能开关
  let features = { ...page.data.features };
  
  switch (modeId) {
    case 'dichromatic':
      features.color = true;
      features.night = false;
      features.motion = false;
      break;
    case 'acuity':
      features.color = false;
      features.night = false;
      features.motion = false;
      break;
    case 'nightVision':
      features.color = true;
      features.night = true;
      features.motion = false;
      break;
    case 'motionVision':
      features.color = true;
      features.night = true;
      features.motion = true;
      break;
  }
  
  page.setData({
    currentVisionMode: modeId,
    features: features,
    showVisionModeSelector: false
  });
  
  // 立即应用新的视觉模式
  if (page.processFrameWebGL && page._lastFrame) {
    page.processFrameWebGL(page._lastFrame);
  }
}

/**
 * 切换视觉模式详情显示
 * @param {Object} page - 页面实例
 */
function toggleVisionModeDetail(page) {
  page.setData({
    showVisionModeDetail: !page.data.showVisionModeDetail
  });
}

module.exports = {
  changeTab,
  swiperChange,
  toggleView,
  switchCameraPosition,
  toggleVisionParams,
  resetVisionParams,
  onBrightnessChange,
  onContrastChange,
  onResolutionChange,
  onAntiAliasChange,
  toggleAnalysisExpanded,
  toggleControls,
  showCameraSelector,
  hideCameraSelector,
  selectCameraType,
  showResolutionSelector,
  hideResolutionSelector,
  selectResolution,
  showVisionModeSelector,
  hideVisionModeSelector,
  selectVisionMode,
  toggleVisionModeDetail
};
